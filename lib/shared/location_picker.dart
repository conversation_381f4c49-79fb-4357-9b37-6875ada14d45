import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:map_location_picker/map_location_picker.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/shared/theme.dart';

import '../controllers/auth_controller.dart';
import 'const.dart';

class LoactionPickerWid extends StatefulWidget {
  const LoactionPickerWid({super.key});

  @override
  State<LoactionPickerWid> createState() => _LoactionPickerWidState();
}

class _LoactionPickerWidState extends State<LoactionPickerWid> {
  Location? selectedLocation;
  Location? location;
  final searchCtrl = TextEditingController();
  num? allowedRad;
  @override
  void initState() {
    super.initState();
    // Location(lat: 22.307299696630334, lng: 73.17506781863678);
    final configs = Get.find<HomeCtrl>().currentOutlet;
    if (configs != null) {
      location = Location(
        lat: configs.lat.toDouble(),
        lng: configs.long.toDouble(),
      );
      allowedRad = configs.radius;
    }
    if (defaultTargetPlatform == TargetPlatform.android) {
      // ignore: deprecated_member_use
      AndroidGoogleMapsFlutter.useAndroidViewSurface = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            context.pop();
          },
          icon: const Icon(Icons.arrow_back),
        ),
        backgroundColor: Colors.white,
        title: const Text(
          "Delivery Location",
          style: TextStyle(color: Colors.black),
        ),
        iconTheme: const IconThemeData(color: Colors.black),
        actionsPadding: EdgeInsets.only(right: 5),
        actions: [
          TextButton(
            onPressed: () => _onSelected(selectedLocation),
            child: const Text(
              "Done",
              style: TextStyle(fontWeight: FontWeight.bold, color: themeColor),
            ),
          ),
        ],
      ),
      body:
          (location == null || allowedRad == null)
              ? const Center(child: Text("Something went wrong!"))
              : Stack(
                children: [
                  MapLocationPicker(
                    // hideLocationButton: true,
                    hideBackButton: true,
                    searchController: searchCtrl,
                    decoration: InputDecoration(
                      hintText: "Search Location",
                      hintStyle: const TextStyle(color: Colors.grey),
                      border: InputBorder.none,

                      suffixIcon: InkWell(
                        onTap: () {
                          searchCtrl.clear();
                        },
                        child: const Icon(Icons.clear),
                      ),
                    ),

                    hideMapTypeButton: true,

                    maintainBottomViewPadding: true,
                    topCardColor: Colors.white,
                    topCardMargin: EdgeInsets.symmetric(
                      horizontal: 13,
                      vertical: 10,
                    ),

                    hideBottomCard: true,
                    onSuggestionSelected: (p0) {
                      // debugPrint(p0?.result.geometry?.location.lat.toString());
                      selectedLocation = p0?.result.geometry?.location;
                      searchCtrl.text = p0?.result.name ?? "";
                      setState(() {});
                    },
                    onDecodeAddress: (p0) {
                      selectedLocation = p0?.geometry.location;
                      searchCtrl.text = p0?.formattedAddress ?? "";
                      setState(() {});
                    },
                    currentLatLng: LatLng(location!.lat, location!.lng),
                    apiKey: gkForNow,
                    // onNext: _onSelected,
                    origin: location,
                    location: location,
                    radius: allowedRad,
                    components: [Component(Component.country, "IN")],
                  ),
                  // Positioned(
                  //   top: 80,
                  //   right: 15,
                  //   child: FloatingActionButton(
                  //     mini: true,
                  //     backgroundColor: Colors.white,
                  //     onPressed: () async {
                  //       // your logic to move map to current location
                  //       final currentPosition =
                  //           await Geolocator.getCurrentPosition();
                  //       // you may need to call a controller:
                  //       // mapController.animateCamera(...);
                  //     },
                  //     child: Icon(Icons.my_location, color: Colors.black),
                  //   ),
                  // ),
                  /* if (selectedLocation != null)
                    Padding(
                      padding: EdgeInsets.fromLTRB(15, 20, 15, 20),
                      child: Align(
                        alignment: Alignment.bottomCenter,
                        child: Container(
                          padding: EdgeInsets.all(14),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.5),
                                spreadRadius: 5,
                                blurRadius: 7,
                                offset: const Offset(0, 3),
                              ),
                            ],
                            color: Colors.white,
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                searchCtrl.text,
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              SizedBox(height: 10),
                              ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: themeColor,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  minimumSize: Size(double.infinity, 40),
                                ),
                                onPressed: () => _onSelected(selectedLocation),
                                child: Text(
                                  "Done",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 15,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                */
                ],
              ),
    );
  }

  _onSelected(Location? result) {
    // print(result?.toJson());
    try {
      if (result != null) {
        final totalDistance = calculateDistance(
          result.lat,
          result.lng,
          location!.lat,
          location!.lng,
        );
        debugPrint(totalDistance.toString());
        // final allowedRad = Get.find<PageOneCtrl>().pageOneModel?.radius ?? 4;
        if (totalDistance > (allowedRad ?? 6)) {
          showAppSnackBar(
            "Your location must be within $allowedRad kms of Swagat outlet.",
          );
          return;
        }
        // Get.back(result: result);
        context.pop(SelectedLocation(latlng: result, address: searchCtrl.text));
      } else {
        debugPrint("Location is null");
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  double calculateDistance(lat1, lon1, lat2, lon2) {
    var p = 0.017453292519943295;
    var c = cos;
    var a =
        0.5 -
        c((lat2 - lat1) * p) / 2 +
        c(lat1 * p) * c(lat2 * p) * (1 - c((lon2 - lon1) * p)) / 2;
    return 12742 * asin(sqrt(a));
  }
}

class SelectedLocation {
  final Location latlng;
  final String address;

  SelectedLocation({required this.latlng, required this.address});
}
