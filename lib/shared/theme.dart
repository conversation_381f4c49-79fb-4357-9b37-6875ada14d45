import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

const themeColor = Color(0xfff04f5f);

final themeData = ThemeData(
  splashColor: Colors.transparent,
  splashFactory: NoSplash.splashFactory,
  highlightColor: Colors.transparent,
  hoverColor: Colors.transparent,
  colorSchemeSeed: themeColor,
  useMaterial3: true,
  scaffoldBackgroundColor: Colors.white,
  canvasColor: Colors.white,
  appBarTheme: const AppBarTheme(backgroundColor: Colors.white),
  navigationBarTheme: const NavigationBarThemeData(
    surfaceTintColor: Colors.white,
    backgroundColor: Colors.white,
  ),
  textTheme: GoogleFonts.montserratTextTheme(),
  bottomSheetTheme: const BottomSheetThemeData(
    surfaceTintColor: Colors.white70,
    backgroundColor: Color(0xfff5f6fb),
  ),
);
