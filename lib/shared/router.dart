import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:swagat_user/models/order_models/order_model.dart';
import 'package:swagat_user/shared/location_picker.dart';
import 'package:swagat_user/views/account/widgets/about_us.dart';
import 'package:swagat_user/views/account/widgets/coupons.dart';
import 'package:swagat_user/views/account/widgets/search_page.dart';
import 'package:swagat_user/shared/methods.dart';
import 'package:swagat_user/views/account/widgets/support.dart';
import 'package:swagat_user/views/account/widgets/your_orders.dart';
import 'package:swagat_user/views/cart/cart_page.dart';
import 'package:swagat_user/views/order/order_details.dart';
import 'package:swagat_user/views/wrapper/outlet_selection.dart';
import 'package:swagat_user/views/wrapper/wrapper.dart';
import '../controllers/home_controller.dart';
import '../views/Authentication/sign_in.dart';
import '../views/Authentication/verify_otp.dart';
import '../views/account/widgets/adress_page.dart';
import '../views/account/widgets/curr_order.dart';
import '../views/account/widgets/terms_cnd.dart';
import '../views/order/live_order_detail.dart';
import 'error_page.dart';

const homeRoute = Routes.wrapper;

final GoRouter appRouter = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: Routes.signin,
  routes: _routes,
  redirect: redirector,
  errorBuilder: (context, state) => const ErrorPage(),
);

FutureOr<String?> redirector(BuildContext context, GoRouterState state) {
  // routeHistory.add(state.uri.path);
  // if (isLoggedIn() && state.fullPath == Routes.auth) {
  //   return routeHistory.reversed.elementAt(1);
  //   // return Routes.home;
  // }
  // return isLoggedIn()
  //     ? (state.fullPath == Routes.signin ? Routes.home : null)
  //     : state.fullPath == Routes.otp
  //         ? null
  //         : Routes.signin;
  if (isLoggedIn()) {
    if (state.fullPath == Routes.signin || state.fullPath == Routes.otp) {
      if (Get.isRegistered<HomeCtrl>()) {
        Future.delayed(const Duration(milliseconds: 10))
            .then((value) => Get.find<HomeCtrl>().update());
      }
      return homeRoute;
    } else {
      if (Get.isRegistered<HomeCtrl>()) {
        Get.find<HomeCtrl>().update();
      } else {
        if (state.fullPath != Routes.otp) Get.put(HomeCtrl());
      }
      return null;
    }
  } else {
    return state.fullPath == Routes.otp ? null : Routes.signin;
  }
}

List<RouteBase> get _routes {
  return <RouteBase>[
    // GoRoute(
    //   path: '${Routes.category}/:name',
    //   pageBuilder: (BuildContext context, GoRouterState state) =>
    //       NoTransitionPage(
    //           child: CategoryWid(
    //     name: state.pathParameters['name'] ?? "",
    //   )),
    // ),
/*     ShellRoute(
        builder: (context, state, child) {
          if (!Get.isRegistered<HomeCtrl>()) Get.put(HomeCtrl());
          return BaseWid(child: child);
        },
        routes: [
          GoRoute(
            path: Routes.home,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: HomeScreen()),
          ),
          GoRoute(
            path: Routes.menu,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: MenuScreen()),
          ),
          GoRoute(
            path: Routes.fav,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: FavouriteScreen()),
          ),
          GoRoute(
            path: Routes.account,
            pageBuilder: (BuildContext context, GoRouterState state) =>
                const NoTransitionPage(child: AccountScreen()),
          ),
        ]), */
    GoRoute(
      path: Routes.wrapper,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: BaseWid()),
    ),
    GoRoute(
      path: Routes.selectOutlet,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: OutletSelectionWid()),
    ),
    GoRoute(
      path: Routes.signin,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: SignInScreen()),
    ),
    GoRoute(
      path: Routes.otp,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: VerifyOtp()),
    ),
    GoRoute(
      path: Routes.cart,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: CartPage()),
    ),
    GoRoute(
      path: Routes.search,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: SearchPage()),
    ),
    GoRoute(
      path: Routes.address,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: AddressPage()),
    ),
    GoRoute(
      path: Routes.currOrder,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: CurrentOrder()),
    ),
    GoRoute(
      path: Routes.coupon,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: Coupons()),
    ),
    GoRoute(
      path: Routes.support,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: Support()),
    ),
    GoRoute(
      path: Routes.aboutus,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: AboutUs()),
    ),
    GoRoute(
      path: Routes.tandc,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: TermsCondition()),
    ),
    GoRoute(
      path: Routes.location,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: LoactionPickerWid()),
    ),
    GoRoute(
      path: Routes.yourOrders,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(child: YourOrdersPage()),
    ),
    GoRoute(
      path: '${Routes.orders}/:oId',
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: OrderDetails(
        orderModel: state.extra as OrderModel,
        orderId: state.pathParameters['oId'] ?? "",
      )),
    ),
    GoRoute(
      path: '${Routes.liveorder}/:oId',
      pageBuilder: (BuildContext context, GoRouterState state) =>
          NoTransitionPage(
              child: LiveOrderDetailsPage(
        orderId: state.pathParameters['oId'] ?? "",
      )),
    ),
  ];
}

class Routes {
  static const liveorder = '/liveorder';
  static const orders = '/orders';
  static const yourOrders = '/yourOrders';
  static const wrapper = '/wrapper';
  static const location = '/location';
  static const signin = '/signin';
  static const otp = '/otp';
  static const home = '/home';
  static const menu = '/menu';
  static const fav = '/favourite';
  static const account = '/account';
  static const selectOutlet = '/selectOutlet';
  static const cart = '/cart';
  static const search = '/search';
  static const address = '/address';
  static const currOrder = '/currentorder';
  static const coupon = '/coupon';
  static const support = '/support';
  static const aboutus = '/aboutus';
  static const tandc = '/terms&condition';
}

  // return isLoggedIn()
  //     ? (state.uri.path == Routes.auth ? Routes.home : null)
  //     : Routes.auth;
