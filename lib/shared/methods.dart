import 'dart:math';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:just_audio/just_audio.dart';
import 'package:map_location_picker/map_location_picker.dart';
import 'package:swagat_user/controllers/auth_controller.dart';
import 'const.dart';
import 'firebase.dart';

bool isLoggedIn() => FBAuth.auth.currentUser != null;

Future<dynamic> showDragableSheet(
  BuildContext context,
  Widget child, {
  bool showDragHandle = true,
}) {
  return showModalBottomSheet(
    showDragHandle: showDragHandle,

    // backgroundColor: Colors.grey[100],
    useRootNavigator: true,
    isScrollControlled: true,
    context: context,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder:
        (context) => DraggableScrollableSheet(
          expand: false,
          initialChildSize: .7,
          minChildSize: .4,
          builder: (context, scrollController) {
            return SingleChildScrollView(
              controller: scrollController,
              child: child,
            );
          },
        ),
  );
}

extension MetaWid on DateTime {
  String goodDayMonth() {
    try {
      return DateFormat('dd MMM').format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodDate() {
    try {
      return DateFormat.yMMMM().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodDayDate() {
    try {
      return DateFormat.yMMMMd().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodDayDate2() {
    try {
      return DateFormat.yMMMd().format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }

  String goodTime() {
    try {
      return DateFormat('hh:mm a').format(this);
    } catch (e) {
      return toString().split(" ").first;
    }
  }
}

bool listsAreSame(List l1, List l2) {
  return l1.where((value) => !l2.contains(value)).isEmpty &&
      l2.where((value) => !l1.contains(value)).isEmpty;
}

const _chars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';
Random _rnd = Random();

String getRandomId(int length) => String.fromCharCodes(
  Iterable.generate(
    length,
    (ctrl) => _chars.codeUnitAt(_rnd.nextInt(_chars.length)),
  ),
);

Future<String?> getOrderId(String outletDocId) async {
  try {
    return await FBFireStore.fb.runTransaction((transaction) async {
      final outletFlags = await transaction.get(
        FBFireStore.outlets.doc(outletDocId).collection('flags').doc('flags'),
      );
      if (outletFlags.exists) {
        final newCount = outletFlags.get('count') + 1;
        transaction.set(
          FBFireStore.outlets.doc(outletDocId).collection('flags').doc('flags'),
          {'count': newCount},
        );
        return newCount.toString().padLeft(4, "0");
      } else {
        transaction.set(
          FBFireStore.outlets.doc(outletDocId).collection('flags').doc('flags'),
          {'count': 1},
        );
        return '0001';
      }
    });
  } catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

double getDistanceBetween(LatLng start, LatLng end) {
  return Geolocator.distanceBetween(
        start.latitude,
        start.longitude,
        end.latitude,
        end.longitude,
      ) /
      1000;
}

String aesEncript(String plainText) {
  return encrypt.Encrypter(
    encrypt.AES(encrypt.Key.fromUtf8(aesKey), mode: encrypt.AESMode.ecb),
  ).encrypt(plainText).base64;
}

// final player = AudioPlayer();

void playOrderConfirmationSound() async {
  // await player.setAsset('assets/sounds/swagat-user.mp3');
  // await player.play();
  // testAssetAvailability();
  await Future.delayed(const Duration(seconds: 1));
  final player = AudioPlayer();
  try {
    await player.setAsset('assets/sounds/swagat-user.mp3');
    await player.play();
  } catch (e) {
    print('Audio error: $e');
    // showAppSnackBar('Audio error: $e');
  } finally {
    await player.dispose();
  }
}

// Future<bool> checkIfAssetExists(String assetPath) async {
//   try {
//     // Attempt to load the asset as byte data
//     final byteData = await rootBundle.load(assetPath);
//     // If no exception, asset exists
//     print(
//       "Asset '$assetPath' loaded successfully, size: ${byteData.lengthInBytes} bytes",
//     );
//     return true;
//   } catch (e) {
//     // If loading failed, asset probably missing in release bundle
//     print("Failed to load asset '$assetPath': $e");
//     return false;
//   }
// }

// void testAssetAvailability() async {
//   bool exists = await checkIfAssetExists('assets/sounds/swagat-user.mp3');
//   if (exists) {
//     showAppSnackBar('Asset is available in this build');
//   } else {
//     showAppSnackBar('Asset is NOT available or missing in this build');
//   }
// }
