const double mobileMinSize = 768;
const double desktopMinSize = 1024;
const mobileMinSize2 = mobileMinSize - 200;
const mobileMinSize3 = mobileMinSize + 200;
const desktopMinSize2 = desktopMinSize - 200;

class OrderType {
  static const delivery = "Delivery";
  static const takeAway = "Take Away";
}

const rsSymbol = '₹';

const foodTimeFrameCheckInterval = 10;
const gkForNow = "AIzaSyCdEZN319f6HBV6w_wat4P-B50y1eBObug";
const aesKey = "1300010590405020";

class OrderStatus {
  static const pending = "Pending";
  static const delivered = "Delievered";
  static const cancelled = "Cancelled";
}
