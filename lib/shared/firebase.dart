import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class FBAuth {
  static final auth = FirebaseAuth.instance;
}

class FBFireStore {
  static final fb = FirebaseFirestore.instance;
  // static final sets = fb.collection('sets').doc('sets');
  static final admins = fb.collection('admin');
  static final outlets = fb.collection('outlet');
  static final categories = fb.collection('category');
  static final addOn = fb.collection('addon');
  static final foods = fb.collection('food');
  // static final riders = fb.collection('rider');
  static final coupons = fb.collection('coupon');
  static final users = fb.collection('users');
  static final orders = fb.collection('orders');
  static final rider = fb.collection('rider');
}

// class FBStorage {
//   static final fbstore = FirebaseStorage.instance;
//   static final category = fbstore.ref().child('category');
//   static final food = fbstore.ref().child('food');
//   static final temp = fbstore.ref().child('temp');
//   // static final otherCertis = fb.ref().child('otherCertis');
// }

class FBFunctions {
  static final ff = FirebaseFunctions.instance;
}
