import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_cashfree_pg_sdk/api/cferrorresponse/cferrorresponse.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpayment/cfwebcheckoutpayment.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfpaymentgateway/cfpaymentgatewayservice.dart';
import 'package:flutter_cashfree_pg_sdk/api/cfsession/cfsession.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfenums.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfexceptions.dart';
import 'package:get/get.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/shared/firebase.dart';

// import 'package:razorpay_flutter/razorpay_flutter.dart';

class PaymentGatewayClass {
  var cfPaymentGatewayService = CFPaymentGatewayService();
  bool? paymentSuccess;
  static const testMode = true;
  final keyId =
      testMode
          ? "TEST10123975a333a6e0eb83e89dbdea57932101"
          : '6287451cdb65a428ad2c1dbd20547826';

  final keySecret =
      testMode
          ? "cfsk_ma_test_3e6b341fb59c70267b918f6e45edd567_fa11f57d"
          : 'cfsk_ma_prod_55e7b08a580ca8beb2d06f1027ff62cf_08c04005';

  Future<bool> createCashfreeOrderPayment({
    required String orderId,
    required String receiptNo,
    required double amount,
  }) async {
    final completer = Completer<bool>();
    try {
      final result = await FBFunctions.ff.httpsCallable('placeOrder').call({
        // 'amount': amount * 100,
        'amount': amount,
        // 'receiptNo': receiptNo,
        'receiptNo': orderId,
        'customer_id': Get.find<HomeCtrl>().currentUserData?.docId,
        'customer_phone': Get.find<HomeCtrl>().currentUserData?.number,
      });
      final response = result.data;

      // debugPrint("-----------------------clf${response.toString()}");
      debugPrint(response['id'].toString());
      await _initCfPayment(
        orderData: response,
        onSuccess: () => completer.complete(true),
        onFailure: () => completer.complete(false),
      );
    } catch (e) {
      debugPrint(e.toString());
    }
    return completer.future;
    // try {} catch (e) {
    //   debugPrint(e.toString());
    // }
  }

  _initCfPayment({
    required dynamic orderData,
    required VoidCallback onSuccess,
    required VoidCallback onFailure,
  }) async {
    try {
      try {
        var session =
            CFSessionBuilder()
                .setEnvironment(CFEnvironment.SANDBOX)
                // .setEnvironment(CFEnvironment.PRODUCTION)
                .setOrderId(orderData['order_id'])
                .setPaymentSessionId(orderData['payment_session_id'])
                .build();
        // return session;
        var cfWebCheckout =
            CFWebCheckoutPaymentBuilder().setSession(session).build();
        cfPaymentGatewayService.setCallback(
          (String orderId) {
            debugPrint(" Payment verified for orderId: $orderId");
            onSuccess(); // Call success handler
          },
          (CFErrorResponse error, String orderId) {
            debugPrint(
              " Payment failed for orderId: $orderId — ${error.getMessage()}",
            );
            onFailure(); // Call failure handler
          },
        );
        cfPaymentGatewayService.doPayment(cfWebCheckout);
      } on CFException catch (e) {
        debugPrint(e.message);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  // void verifyPayment(String orderId) {
  //   debugPrint("Payment verified for orderId: $orderId");

  //   // Optional: verify with backend if needed

  //   paymentSuccess = true;

  //   // Navigate to home page
  //   // Get.offAllNamed(Routes.homepage);
  // }

  // void onError(CFErrorResponse errorResponse, String orderId) {}

  // _initPayment(String orderId, double amount, String paymentOrderId) async {
  //   try {
  //     var options = {
  //       'key': keyId,
  //       'amount': amount,
  //       'order_id': orderId,
  //       'name': "BOSS LAUNDRY",
  //       'prefill': {'email': FBAuth.auth.currentUser?.email},
  //       'notes': {
  //         'paymentOrderId': paymentOrderId,
  //       }
  //     };
  //     // _paymentListener(orderDocId);
  //     // razorpay.open(options);
  //   } catch (e) {
  //     debugPrint(e.toString());
  //   }
  // }
}
