import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:swagat_user/models/outlet_model.dart';
import 'package:swagat_user/shared/theme.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../controllers/home_controller.dart';
import '../../shared/firebase.dart';
import '../../shared/router.dart';

class OutletSelectionWid extends StatelessWidget {
  const OutletSelectionWid({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Container(
            height: MediaQuery.sizeOf(context).height,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color.fromARGB(255, 241, 88, 77), Colors.red],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
        ],
      ),
      bottomSheet: const ClipRRect(
        clipBehavior: Clip.antiAlias,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(35),
          topRight: Radius.circular(35),
        ),
        child: OutletSheet(fromHomePage: false),
      ),
    );
  }
}

class OutletSheet extends StatelessWidget {
  const OutletSheet({super.key, required this.fromHomePage});
  final bool fromHomePage;

  @override
  Widget build(BuildContext context) {
    final searchController = TextEditingController();
    return StreamBuilder<List<OutletModel>>(
      stream: FBFireStore.outlets.snapshots().map(
        (event) => event.docs.map((e) => OutletModel.fromDocSnap(e)).toList(),
      ),
      builder: (context, snapshot) {
        List<OutletModel> outList = snapshot.data ?? [];

        return OutletListTile(
          outlets: outList,
          searchController: searchController,
          fromHomePage: fromHomePage,
        );
      },
    );
  }
}

class OutletListTile extends StatefulWidget {
  const OutletListTile({
    super.key,
    required this.outlets,
    required this.searchController,
    required this.fromHomePage,
  });

  final List<OutletModel> outlets;
  final TextEditingController searchController;
  final bool fromHomePage;

  @override
  State<OutletListTile> createState() => _OutletListTileState();
}

class _OutletListTileState extends State<OutletListTile> {
  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      snap: true,
      snapSizes: const [.7],
      minChildSize: .5,
      expand: false,
      maxChildSize: 0.8,
      builder: (context, scrollController) {
        final filtered =
            widget.outlets
                .where(
                  (element) => element.outletName.toLowerCase().contains(
                    widget.searchController.text.toLowerCase(),
                  ),
                )
                .toList();
        return SingleChildScrollView(
          controller: scrollController,
          padding: const EdgeInsets.all(20),
          physics: const ClampingScrollPhysics(),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              SizedBox(height: 5),

              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.red.shade50,
                      // border: Border.all(color: Colors, width: .7),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 10,
                      ),
                      child: Center(
                        child: Column(
                          children: [
                            Text(
                              filtered.length.toString(),
                              style: const TextStyle(
                                color: themeColor,
                                // color: Colors.white,
                                fontSize: 17,
                                height: 0,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const Text(
                              "Outlet",
                              style: TextStyle(
                                color: themeColor,
                                // color: Colors.white,
                                fontSize: 12,
                                height: 0,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: widget.searchController,
                      onChanged: (value) {
                        setState(() {});
                      },
                      maxLines: 1,
                      decoration: const InputDecoration(
                        hintText: 'Search',
                        hintStyle: TextStyle(color: Colors.grey),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 14,
                        ),
                        suffixIcon: Icon(CupertinoIcons.search),
                        suffixIconColor: Colors.grey,
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            width: 1,
                            color: Colors.grey,
                            style: BorderStyle.solid,
                          ),
                          borderRadius: BorderRadius.all(Radius.circular(10)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            width: 1,
                            color: Colors.grey,
                            style: BorderStyle.solid,
                          ),
                          borderRadius: BorderRadius.all(Radius.circular(10)),
                        ),
                        disabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                            width: 1,
                            color: Colors.grey,
                            style: BorderStyle.solid,
                          ),
                          borderRadius: BorderRadius.all(Radius.circular(10)),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 20),
              ListView.separated(
                separatorBuilder: (context, index) {
                  return SizedBox(height: 15);
                },
                physics: const ClampingScrollPhysics(),
                itemCount: filtered.length,
                shrinkWrap: true,
                itemBuilder: ((context, index) {
                  bool delTakeCheck =
                      (filtered[index].delivery ||
                          filtered[index].takeAway ||
                          filtered[index].cashOnDelivery);
                  return Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey.shade50,
                      border: Border.all(
                        color: Colors.grey.shade200,
                        width: .7,
                      ),
                      // boxShadow: [
                      //   BoxShadow(
                      //     color: Colors.grey.withOpacity(0.2),
                      //     spreadRadius: 1,
                      //     blurRadius: 5,
                      //     offset: const Offset(0, 3),
                      //   ),
                      // ],
                    ),
                    // decoration: BoxDecoration(
                    //   borderRadius: BorderRadius.circular(15),
                    //   color: Colors.white,
                    //   boxShadow: [
                    //     BoxShadow(
                    //       color: Colors.grey.withOpacity(0.2),
                    //       spreadRadius: 1,
                    //       blurRadius: 5,
                    //       offset: const Offset(0, 3),
                    //     ),
                    //   ],
                    // ),
                    child: Row(
                      children: [
                        RotatedBox(
                          quarterTurns: 15,
                          child: Text(
                            delTakeCheck ? "Active" : "Inactive",
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                              color: delTakeCheck ? Colors.green : Colors.red,
                            ),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: InkWell(
                            borderRadius: BorderRadius.circular(15),
                            onTap: () async {
                              if (context.mounted) {
                                widget.fromHomePage
                                    ? Navigator.of(context).pop()
                                    : context.go(Routes.wrapper);
                              }
                              final ctrl = Get.find<HomeCtrl>();
                              await FBFireStore.users
                                  .doc(ctrl.currentUserData?.docId)
                                  .update({
                                    'selectedOutletDocId':
                                        filtered[index].docId,
                                  })
                                  .then(
                                    (value) async => await FirebaseMessaging
                                        .instance
                                        .subscribeToTopic(
                                          filtered[index]
                                              .outletName
                                              .removeAllWhitespace,
                                        ),
                                  );
                              for (var element in widget.outlets) {
                                if (element.docId != filtered[index].docId) {
                                  await FirebaseMessaging.instance
                                      .unsubscribeFromTopic(
                                        element.outletName.removeAllWhitespace,
                                      );
                                }
                              }
                              // .unsubscribeFromTopic(ctrl.currentOutlet!
                              //     .outletName.removeAllWhitespace));
                              // ctrl.update();
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                vertical: 10,
                                horizontal: 8,
                              ),
                              decoration: BoxDecoration(
                                // color: Colors.white,
                                // color: themeColor.withOpacity(.05),
                                // gradient: LinearGradient(
                                //   colors: [
                                //     Colors.red.shade600,
                                //     Colors.red.shade400,
                                //   ],
                                // ),
                                // border: Border.all(
                                //   color: const Color.fromARGB(
                                //     255,
                                //     143,
                                //     143,
                                //     143,
                                //   ),
                                // ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              child: Row(
                                children: [
                                  // Container(
                                  //   padding: const EdgeInsets.symmetric(
                                  //       vertical: 8, horizontal: 8),
                                  //   decoration: BoxDecoration(
                                  //       color: Colors.grey.shade100,
                                  //       borderRadius:
                                  //           BorderRadius.circular(6)),
                                  //   child: const Text(
                                  //     "NA",
                                  //     style: TextStyle(color: Colors.black),
                                  //   ),
                                  // ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          filtered[index].outletName,
                                          style: const TextStyle(
                                            color: Colors.black,
                                            fontSize: 15,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        Text(
                                          filtered[index].outletAddresss.isEmpty
                                              ? '-'
                                              : filtered[index].outletAddresss,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: const TextStyle(
                                            color: Colors.black,
                                            fontSize: 13,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // const SizedBox(width: 8),
                                  InkWell(
                                    onTap: () async {
                                      try {
                                        var uri = Uri.parse(
                                          'https://www.google.com/maps/search/?api=1&query=${filtered[index].lat},${filtered[index].long}',
                                        );
                                        try {
                                          await launchUrl(
                                            uri,
                                            mode:
                                                LaunchMode.externalApplication,
                                          );
                                        } catch (e) {
                                          debugPrint(e.toString());
                                        }
                                      } catch (e) {
                                        debugPrint(e.toString());
                                      }
                                    },
                                    child: Container(
                                      // color: Colors.grey.shade100,
                                      padding: const EdgeInsets.all(8.0),
                                      child: const Icon(
                                        CupertinoIcons.arrow_up_right_square,
                                        color: themeColor,
                                        size: 25,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),
              SizedBox(height: 12),
            ],
          ),
        );
      },
    );
  }
}
