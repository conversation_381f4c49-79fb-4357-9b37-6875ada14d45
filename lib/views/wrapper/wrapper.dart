// ignore_for_file: no_wildcard_variable_uses

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/shared/firebase.dart';
import 'package:swagat_user/shared/theme.dart';
import 'package:swagat_user/views/account/account.dart';
import 'package:swagat_user/views/favourite/favourite.dart';
import 'package:swagat_user/views/homepage/home.dart';
import 'package:swagat_user/views/menupage/menu_page.dart';
import 'package:swagat_user/views/navbar/navbar_tile.dart';
import '../../shared/router.dart';
import 'outlet_selection.dart';

class BaseWid extends StatelessWidget {
  const BaseWid({super.key});
  // final Widget child;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        if (ctrl.currentUserData != null) {
          if (ctrl.currentUserData!.selectedOutletDocId == null) {
            return const OutletSelectionWid();
          } else {
            return ctrl.currentOutlet != null
                ? const Wrapper()
                : const AppLoader();
          }
        }
        return const AppLoader();
      },
    );
  }
}

class Wrapper extends StatelessWidget {
  const Wrapper({super.key});
  // final Widget child;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        return Scaffold(
          backgroundColor: Colors.white,
          body:
              [
                const HomeScreen(),
                const MenuScreen(),
                const FavouriteScreen(),
                const AccountScreen(),
              ][ctrl.selectedIndex],
          bottomNavigationBar: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (ctrl.cartItems.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 13.0),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: Colors.white,
                      boxShadow: const [
                        BoxShadow(
                          blurRadius: 10,
                          color: Colors.black12,
                          offset: Offset(0, 0),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        const SizedBox(width: 10),
                        // const CircleAvatar(
                        //     backgroundColor: Colors.white,
                        //     child: Center(
                        //         child: Icon(CupertinoIcons.cart,
                        //             color: themeColor))),
                        CircleAvatar(
                          backgroundColor: Colors.white,
                          child: Image.asset('assets/swagat2.png'),
                        ),
                        const SizedBox(width: 15),
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              ctrl.currentOutlet?.outletName ?? '-',
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            Text(
                              "${ctrl.cartItems.length} items",
                              style: const TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const Spacer(),
                        InkWell(
                          onTap: () => context.push(Routes.cart),
                          highlightColor: Colors.transparent,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 10,
                              horizontal: 10,
                            ),
                            decoration: BoxDecoration(
                              color: themeColor,
                              borderRadius: BorderRadius.circular(7),
                            ),
                            child: const Text(
                              'View Cart',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w800,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        InkWell(
                          onTap: () async {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return AlertDialog(
                                  backgroundColor: Colors.white,
                                  surfaceTintColor: Colors.white,
                                  title: const Text('Clear Cart'),
                                  content: const Text(
                                    'Are you sure you want to clear cart ?',
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () async {
                                        ctrl.clearCart();
                                        if (context.mounted) {
                                          Navigator.of(context).pop();
                                        }
                                      },
                                      child: const Text('Clear'),
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: const Text('Cancel'),
                                    ),
                                  ],
                                );
                              },
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.all(7),
                            decoration: const BoxDecoration(
                              color: Color(0xfff5f6fb),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(CupertinoIcons.xmark, size: 10),
                          ),
                        ),
                        // IconButton(
                        //     style: IconButton.styleFrom(
                        //         padding: EdgeInsets.zero,
                        //         backgroundColor: const Color(0xfff5f6fb)),
                        //     onPressed: () {},
                        //     icon: const Icon(
                        //       CupertinoIcons.xmark,
                        //       size: 15,
                        //     )),
                        const SizedBox(width: 10),
                      ],
                    ),
                  ),
                ),
              if (ctrl.cartItems.isNotEmpty) const SizedBox(height: 10),
              Container(
                decoration: const BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      offset: Offset(0, 0),
                      blurStyle: BlurStyle.outer,
                    ),
                  ],
                ),
                child: NavigationBar(
                  elevation: 10,
                  backgroundColor: Colors.white,
                  destinations: navitems(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  List<Widget> navitems() {
    return const [
      NavbarTile(icon: CupertinoIcons.home, textName: 'Home', selectedIndex: 0),
      NavbarTile(
        icon: Icons.fastfood_outlined,
        textName: 'Menu',
        selectedIndex: 1,
      ),
      NavbarTile(
        icon: CupertinoIcons.heart,
        textName: 'Favourite',
        selectedIndex: 2,
      ),
      NavbarTile(
        icon: CupertinoIcons.settings,
        textName: 'Account',
        selectedIndex: 3,
      ),
    ];
  }
}

class AppLoader extends StatelessWidget {
  const AppLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: LoadingAnimationWidget.fourRotatingDots(
          size: 40,
          color: themeColor,
        ),
      ),
    );
  }
}
