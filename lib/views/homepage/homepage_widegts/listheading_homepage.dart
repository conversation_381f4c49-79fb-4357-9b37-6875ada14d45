import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../shared/theme.dart';

class ListHeading extends StatelessWidget {
  const ListHeading({
    super.key,
    required this.title,
  });
  final String title;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Row(
        children: [
          const Expanded(child: Divider(thickness: 0.2, color: Colors.grey)),
          const SizedBox(width: 7),
          Center(
            child: Text(
              title.toUpperCase(),
              style: GoogleFonts.montserrat(
                textStyle: const TextStyle(
                  letterSpacing: 2.3,
                  fontWeight: FontWeight.w500,
                  color: themeColor,
                ),
              ),
            ),
          ),
          const SizedBox(width: 7),
          const Expanded(child: Divider(thickness: 0.2, color: Colors.grey)),
        ],
      ),
    );
  }
}
