import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';

class Carousel extends StatefulWidget {
  const Carousel({super.key});
  @override
  State<Carousel> createState() => _CarouselState();
}

int currentIndex = 0;
final items = [
  'assets/Carousel1.png',
  'assets/Carousel2.png',
  'assets/Carousel3.png',
];

class _CarouselState extends State<Carousel> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CarouselSlider(
          options: CarouselOptions(
            autoPlayInterval: const Duration(seconds: 3),
            autoPlay: true,
            aspectRatio: 2,
            viewportFraction: .9,
            enlargeCenterPage: false,
            onPageChanged: (index, reason) {
              setState(() {
                currentIndex = index;
              });
            },
          ),
          items:
              items
                  .map(
                    (e) => Padding(
                      padding: const EdgeInsets.only(right: 20.0, bottom: 15),
                      child: Container(
                        clipBehavior: Clip.antiAlias,
                        decoration: ShapeDecoration(
                          shape: ContinuousRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                        child: SizedBox.expand(
                          child: Image.asset(e, fit: BoxFit.cover),
                        ),
                      ),
                    ),
                  )
                  .toList(),
        ),
        DotsIndicator(
          dotsCount: items.length,
          position: currentIndex.toDouble(),
        ),
      ],
    );
  }
}
