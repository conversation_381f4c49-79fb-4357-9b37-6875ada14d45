import 'package:flutter/material.dart';

class VegIconWid extends StatelessWidget {
  const VegIconWid({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: const Border(
          bottom: BorderSide(color: Colors.green),
          top: BorderSide(color: Colors.green),
          left: BorderSide(color: Colors.green),
          right: BorderSide(color: Colors.green),
        ),
      ),
      height: 15,
      width: 15,
      child: const Center(
        child: Icon(
          Icons.circle,
          color: Colors.green,
          size: 8,
        ),
      ),
    );
  }
}
