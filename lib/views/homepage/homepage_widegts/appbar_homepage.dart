// ignore_for_file: no_wildcard_variable_uses

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/shared/router.dart';
import 'package:swagat_user/shared/theme.dart';
import '../../wrapper/outlet_selection.dart';
import 'package:badges/badges.dart' as badges;

class HomepageAppbar extends StatelessWidget {
  const HomepageAppbar({
    super.key,
    this.extraTabs,
    this.extraHeight = 0,
    this.pinned = false,
    this.searchController,
    this.searchPage = false,
  });
  final Widget? extraTabs;
  final double extraHeight;
  final bool pinned;
  final bool searchPage;
  final TextEditingController? searchController;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        return SliverAppBar(
          elevation: 10,
          scrolledUnderElevation: 5,
          shadowColor: Colors.black38,
          centerTitle: false,
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          pinned: pinned,
          snap: true,
          floating: true,
          // expandedHeight: 100,
          title: _title(context, ctrl),
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(70 + extraHeight),
            child: Align(
              alignment: Alignment.bottomLeft,
              child: Padding(
                padding: const EdgeInsets.all(14),
                child: Column(
                  children: [
                    InkWell(
                      onTap:
                          searchPage ? () => context.push(Routes.search) : null,
                      child: _SearchField(
                        searchController: searchController,
                        searchPage: searchPage,
                        onChanged: () => ctrl.update(),
                      ),
                    ),
                    if (extraTabs != null) extraTabs!,
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Padding _title(BuildContext context, HomeCtrl ctrl) {
    return Padding(
      padding: const EdgeInsets.only(top: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () {
              showModalBottomSheet(
                backgroundColor: Colors.white,
                useRootNavigator: true,
                isScrollControlled: true,
                context: context,
                builder: (context) {
                  return const OutletSheet(fromHomePage: true);
                },
              );
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Row(
                  children: [
                    Icon(
                      CupertinoIcons.location_solid,
                      color: Colors.red.shade400,
                      size: 17,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      ctrl.currentOutlet!.outletName,
                      style: const TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Icon(
                      Icons.keyboard_arrow_down_rounded,
                      size: 22,
                      color: Colors.black,
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 2.0),
                  child: Text(
                    ctrl.currentOutlet!.outletAddresss,
                    style: const TextStyle(fontSize: 13, color: Colors.black),
                  ),
                ),
              ],
            ),
          ),
          // SizedBox(
          //   child: Stack(
          //     alignment: Alignment.topRight,
          //     children: [
          //       IconButton(
          //         style: IconButton.styleFrom(hoverColor: Colors.white),
          //         onPressed: () {
          //           context.push(Routes.cart);
          //         },
          //         icon: const Icon(
          //           CupertinoIcons.cart,
          //           size: 28,
          //           color: themeColor,
          //         ),
          //       ),
          //       CircleAvatar(
          //         backgroundColor: themeColor,
          //         radius: 8,
          //         child: Text(
          //           // ctrl.cartItems.length.toString(),
          //           "99",
          //           style: const TextStyle(fontSize: 10, color: Colors.white),
          //         ),
          //       )
          //     ],
          //   ),
          // ),
          // Badge.count(
          //   offset: const Offset(3, 0),
          //   backgroundColor: themeColor,
          //   isLabelVisible: ctrl.cartItems.isNotEmpty,
          //   count: ctrl.cartItems.length,
          //   child: IconButton(
          //     style: IconButton.styleFrom(hoverColor: Colors.white),
          //     onPressed: () => context.push(Routes.cart),
          //     icon: const Icon(
          //       CupertinoIcons.cart,
          //       size: 28,
          //       color: themeColor,
          //     ),
          //   ),
          // ),
          badges.Badge(
            position: badges.BadgePosition.topEnd(top: -6.5, end: -1.5),
            showBadge: ctrl.cartItems.isNotEmpty,
            badgeStyle: const badges.BadgeStyle(
              badgeColor: themeColor,
              padding: EdgeInsets.all(6),
              shape: badges.BadgeShape.circle,
            ),
            badgeContent: Text(
              ctrl.cartItems.length.toString(),
              style: const TextStyle(color: Colors.white, fontSize: 13),
            ),
            badgeAnimation: const badges.BadgeAnimation.slide(
              slideTransitionPositionTween: badges.SlideTween(
                begin: Offset(4, -10),
                end: Offset(0, 0),
              ),
            ),
            child: IconButton(
              onPressed: () => context.push(Routes.cart),
              icon: const Icon(CupertinoIcons.bag, size: 30, color: themeColor),
            ),
          ),
        ],
      ),
    );
  }
}

class _SearchField extends StatelessWidget {
  const _SearchField({
    required this.searchController,
    required this.onChanged,
    required this.searchPage,
  });
  final TextEditingController? searchController;
  final Function onChanged;
  final bool searchPage;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: TextField(
            enabled: !searchPage,
            onChanged: (value) => onChanged(),
            controller: searchController,
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(vertical: 12),
              hintText: "What's on Your Mind",
              hintStyle: const TextStyle(color: Color(0xff717274)),
              fillColor: const Color(0xfff2f2f2),
              filled: true,
              border: OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.circular(12),
              ),
              // enabledBorder: const OutlineInputBorder(
              //   borderSide: BorderSide(
              //     width: 1,
              //     color: Colors.grey,
              //     style: BorderStyle.solid,
              //   ),
              //   borderRadius: BorderRadius.all(
              //     Radius.circular(12),
              //   ),
              // ),
              // focusedBorder: const OutlineInputBorder(
              //   borderSide: BorderSide(
              //     width: 1,
              //     color: Colors.grey,
              //     style: BorderStyle.solid,
              //   ),
              //   borderRadius: BorderRadius.all(
              //     Radius.circular(12),
              //   ),
              // ),
              // disabledBorder: const OutlineInputBorder(
              //   borderSide: BorderSide(
              //     width: 1,
              //     color: Colors.grey,
              //     style: BorderStyle.solid,
              //   ),
              //   borderRadius: BorderRadius.all(
              //     Radius.circular(12),
              //   ),
              // ),
              prefixIcon: const Icon(
                CupertinoIcons.search,
                size: 19,
                color: Color(0xff717274),
              ),
              suffixIcon:
                  searchController?.text.isEmpty ?? true
                      ? null
                      : InkWell(
                        onTap: () {
                          searchController?.clear();
                          Get.find<HomeCtrl>().update();
                        },
                        child: const Icon(
                          CupertinoIcons.xmark_circle_fill,
                          color: Colors.grey,
                        ),
                      ),
            ),
          ),
        ),
      ],
    );
  }
}
