// ignore_for_file: no_wildcard_variable_uses

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/home_controller.dart';

class HomepageCatlist extends StatelessWidget {
  const HomepageCatlist({
    super.key,
    required this.catImageWidth,
    required this.catGridSpacing,
  });

  final double catImageWidth;
  final double catGridSpacing;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.only(left: 14.0, top: 20, right: 14),
        child: GetBuilder<HomeCtrl>(
          builder: (ctrl) {
            return ctrl.categories.isEmpty
                ? const Center(child: CircularProgressIndicator())
                : SizedBox(
                  width:
                      (catImageWidth + (catGridSpacing)) *
                      (ctrl.categories.length > 4
                          ? (ctrl.categories.length / 2).round()
                          : ctrl.categories.length),
                  child: GridView.count(
                    shrinkWrap: true,
                    padding: EdgeInsets.zero,
                    physics: const ClampingScrollPhysics(),
                    childAspectRatio: .65,
                    // axisDirection: AxisDirection.down,
                    // mainAxisSpacing: catGridSpacing,
                    crossAxisSpacing: catGridSpacing,
                    crossAxisCount:
                        ctrl.categories.length > 4
                            ? (ctrl.categories.length / 2).round()
                            : ctrl.categories.length,
                    children:
                        ctrl.categories
                            .map(
                              (e) => InkWell(
                                onTap: () async {
                                  ctrl.selectedIndex = 1;
                                  ctrl.update();
                                  await Future.delayed(Durations.short1);
                                  Scrollable.ensureVisible(
                                    e.catKey.currentContext!,
                                    duration: Durations.extralong4,
                                    curve: Curves.decelerate,
                                  );
                                },
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Material(
                                      color: Colors.white,
                                      child: Container(
                                        height: catImageWidth,
                                        width: catImageWidth,
                                        decoration: const BoxDecoration(
                                          color: Colors.white,
                                        ),
                                        child: CachedNetworkImage(
                                          imageUrl: e.image,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        e.name,
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            )
                            .toList(),
                  ),
                );
          },
        ),
      ),
    );
  }
}
