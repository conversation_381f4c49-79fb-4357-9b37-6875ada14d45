// ignore_for_file: no_wildcard_variable_uses

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:swagat_user/controllers/auth_controller.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/models/food_model.dart';
import 'package:swagat_user/shared/theme.dart';
import 'package:swagat_user/views/homepage/homepage_widegts/veg_icon.dart';
import '../../../shared/methods.dart';
import '../../cart/add_food_sheet.dart';
import '../../menupage/menupage_widgets/foodcard_bottomsheet.dart';

class SpecialItemCard extends StatefulWidget {
  const SpecialItemCard({super.key, required this.food});
  final FoodModel food;

  @override
  State<SpecialItemCard> createState() => _SpecialItemCardState();
}

class _SpecialItemCardState extends State<SpecialItemCard> {
  bool loading = false;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final crtItm = ctrl.cartItems.firstWhereOrNull(
          (element) =>
              element.foodDocId == widget.food.docId &&
              element.variantId == widget.food.variants.first.id &&
              listsAreSame(element.addonDocIdList, widget.food.addOns),
        );
        // final favItems = ctrl.currentUserData!.favourite;
        final variant = widget.food.variants.firstWhereOrNull(
          (element) => element.id == crtItm?.variantId,
        );
        return Opacity(
          opacity: widget.food.availableNow ? 1 : .5,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 14),
            child: InkWell(
              onTap: () {
                showDragableSheet(
                  context,
                  showDragHandle: false,
                  BottomSheetFoodCard(foodDocId: widget.food.docId),
                );
              },
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 12.0),
                    child: Row(
                      children: [
                        Expanded(flex: 2, child: _bestSellerColumn()),
                        const SizedBox(width: 8),
                        IntrinsicWidth(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Material(
                                color: Colors.white,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: CachedNetworkImage(
                                    imageUrl: widget.food.imageUrl,
                                    height: 100,
                                    width: 100,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 5),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  crtItm != null &&
                                          (widget.food.variants.length == 1 &&
                                              widget.food.addOns.isEmpty)
                                      ? loading
                                          ? SizedBox(
                                            height: 33,
                                            child:
                                                LoadingAnimationWidget.progressiveDots(
                                                  color: const Color(
                                                    0xffd47c85,
                                                  ),
                                                  size: 20,
                                                ),
                                          )
                                          : Container(
                                            width: 100,
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: Row(
                                              children: [
                                                SizedBox(
                                                  height: 32.5,
                                                  width: 32.5,
                                                  child: IconButton(
                                                    padding: EdgeInsets.zero,
                                                    style: IconButton.styleFrom(
                                                      elevation: 0,
                                                      backgroundColor:
                                                          themeColor,
                                                      shape: RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              6,
                                                            ),
                                                      ),
                                                    ),
                                                    onPressed: () async {
                                                      setState(() {
                                                        loading = true;
                                                      });
                                                      await ctrl.removeFromCart(
                                                        widget.food.docId,
                                                        variant!.id,
                                                      );
                                                      setState(() {
                                                        loading = false;
                                                      });
                                                    },
                                                    icon: const Icon(
                                                      CupertinoIcons.minus,
                                                      size: 18,
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Container(
                                                    color: Colors.white,
                                                    child: Text(
                                                      crtItm.qty.toString(),
                                                      textAlign:
                                                          TextAlign.center,
                                                      style: const TextStyle(
                                                        fontSize: 17,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                SizedBox(
                                                  height: 32.5,
                                                  width: 32.5,
                                                  child: IconButton(
                                                    padding: EdgeInsets.zero,
                                                    style: IconButton.styleFrom(
                                                      elevation: 0,
                                                      backgroundColor:
                                                          themeColor,
                                                      shape: RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              6,
                                                            ),
                                                      ),
                                                    ),
                                                    onPressed: () async {
                                                      if (!widget
                                                          .food
                                                          .availableNow) {
                                                        return;
                                                      }
                                                      setState(() {
                                                        loading = true;
                                                      });
                                                      await ctrl.addToCart(
                                                        widget.food.docId,
                                                        variant!.id,
                                                        [],
                                                        userCartItem: crtItm,
                                                      );
                                                      setState(() {
                                                        loading = false;
                                                      });
                                                    },
                                                    icon: const Icon(
                                                      CupertinoIcons.add,
                                                      size: 18,
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )
                                      : SizedBox(
                                        height: 33,
                                        child: ElevatedButton.icon(
                                          style: ElevatedButton.styleFrom(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 13,
                                            ),
                                            backgroundColor: const Color(
                                              0xfffff6f7,
                                            ),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                            ),
                                            elevation: 0,
                                            side: const BorderSide(
                                              color: Color(0xffd47c85),
                                              width: .9,
                                            ),
                                          ),
                                          onPressed: () {
                                            if (widget.food.variants.length ==
                                                    1 &&
                                                widget.food.addOns.isEmpty) {
                                              if (widget.food.availableNow) {
                                                ctrl.addToCart(
                                                  widget.food.docId,
                                                  widget.food.variants.first.id,
                                                  [],
                                                  userCartItem: crtItm,
                                                );
                                              } else {
                                                showAppSnackBar(
                                                  'Currently not available',
                                                );
                                              }
                                            } else {
                                              showModalBottomSheet(
                                                useRootNavigator: true,
                                                showDragHandle: true,
                                                context: context,
                                                builder:
                                                    (context) => AddToCartSheet(
                                                      foodDocId:
                                                          widget.food.docId,
                                                    ),
                                              );
                                            }
                                          },
                                          icon: const Icon(
                                            Icons.add,
                                            size: 19,
                                            color: Color(0xffd47c85),
                                          ),
                                          label: const Text(
                                            'ADD',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 15,
                                              color: Color(0xffd47c85),
                                            ),
                                          ),
                                        ),
                                      ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Column _bestSellerColumn() {
    final tmp = widget.food.variants.map((e) => e.price).toList();
    tmp.sort();
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        widget.food.special ? _bestSellerRow() : const SizedBox(),
        const SizedBox(height: 5),
        Text(
          widget.food.name.capitalizeFirst!,
          style: GoogleFonts.aBeeZee(
            textStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
        ),
        const SizedBox(height: 5),
        Text(
          tmp.isEmpty ? "-" : '₹${tmp.first}',
          style: GoogleFonts.aBeeZee(
            textStyle: const TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 15,
            ),
          ),
        ),
        const SizedBox(height: 5),
        Text(
          widget.food.description,
          maxLines: 2,
          style: GoogleFonts.aBeeZee(
            textStyle: const TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 13,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ],
    );
  }

  Row _bestSellerRow() {
    return const Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        VegIconWid(),
        SizedBox(width: 5),
        Icon(Icons.star_outline_sharp, size: 15, color: themeColor),
        Text("Bestseller", style: TextStyle(color: themeColor)),
      ],
    );
  }
}
