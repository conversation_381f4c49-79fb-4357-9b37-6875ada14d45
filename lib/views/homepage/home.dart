// ignore_for_file: no_wildcard_variable_uses

import 'dart:developer';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:swagat_user/views/homepage/homepage_widegts/special_item_widget.dart';
import '../../controllers/home_controller.dart';
import '../../main.dart';
import 'homepage_widegts/appbar_homepage.dart';
import 'homepage_widegts/catlist_homepage.dart';
import 'homepage_widegts/carousel_homepage.dart';
import 'homepage_widegts/listheading_homepage.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final catGridSpacing = 20.0;
  @override
  void initState() {
    super.initState();
    setupFcm();
  }

  @override
  Widget build(BuildContext context) {
    final catImageWidth = MediaQuery.sizeOf(context).width * .22;

    return Scaffold(
      body: GetBuilder<HomeCtrl>(
        builder: (ctrl) {
          final filtered =
              ctrl.foods.where((element) => element.special).toList();
          return GestureDetector(
            onTap: () => FocusScope.of(context).unfocus(),
            child: CustomScrollView(
              slivers: <Widget>[
                // A P P B A R
                const HomepageAppbar(searchPage: true),

                // M A I N   B O D Y
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 15.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // C A R O U S E L
                        const Carousel(),

                        // H E A D I  N G
                        const ListHeading(title: "Categories"),

                        // C A T E G O R Y   L I S T
                        if (ctrl.categories.isNotEmpty)
                          HomepageCatlist(
                            catImageWidth: catImageWidth,
                            catGridSpacing: catGridSpacing,
                          ),

                        // H E A D I N G
                        const SizedBox(height: 10),
                        const ListHeading(title: "Special Items"),

                        // S P E C I A L  I T E M S
                        ListView.separated(
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: filtered.length,
                          padding: const EdgeInsets.only(top: 8.0),
                          itemBuilder:
                              (context, index) =>
                                  SpecialItemCard(food: filtered[index]),
                          separatorBuilder: (BuildContext context, int index) {
                            return const Padding(
                              padding: EdgeInsets.symmetric(horizontal: 12.0),
                              child: Divider(thickness: .2),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  setupFcm() async {
    try {
      //flutterLocalNotificationsPlugin.cancelAll();
      FirebaseMessaging messaging = FirebaseMessaging.instance;
      await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        log("Notification Recieved");
        RemoteNotification? notification = message.notification;

        try {
          if (Platform.isAndroid) {
            flutterLocalNotificationsPlugin.show(
              notification.hashCode,
              notification!.title,
              notification.body,
              const NotificationDetails(
                android: androidPlatformChannelSpecifics,
                iOS: iOSPlatformChannelSpecifics,
              ),
            );
          }
        } catch (e) {
          debugPrint(e.toString());
        }
      });
      messaging.subscribeToTopic('global');

      // FlutterAppBadger.removeBadge();
      // App opened from Notification

      // RemoteMessage? initialMessage =
      //     await FirebaseMessaging.instance.getInitialMessage();

      // if (initialMessage != null) {
      //   _handleMessage(initialMessage);
      // }

      FirebaseMessaging.onMessageOpenedApp.listen((event) {
        // Get.to(() => const NotiesPage());
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
