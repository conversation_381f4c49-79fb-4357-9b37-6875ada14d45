// ignore_for_file: no_wildcard_variable_uses

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/views/homepage/homepage_widegts/appbar_homepage.dart';
import 'menupage_widgets/cat_top_button.dart';
import 'menupage_widgets/displaycat_widget.dart';

class MenuScreen extends StatelessWidget {
  const MenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        return Scaffold(
          backgroundColor: const Color(0xfff5f6fb),
          body: GestureDetector(
            onTap: () => FocusScope.of(context).unfocus(),
            child: CustomScrollView(
              slivers: <Widget>[
                // A P P  B A R
                HomepageAppbar(
                  extraHeight: ctrl.categories.isEmpty ? 14 : 60,
                  pinned: true,
                  extraTabs: const CategoriesTab(),
                  searchController: ctrl.searchController3,
                ),

                // B O D Y
                SliverToBoxAdapter(
                  child: ListView(
                    padding: const EdgeInsets.only(top: 13),
                    shrinkWrap: true,
                    physics: const ClampingScrollPhysics(),
                    children:
                        ctrl.categories
                            .map((e) => CategorySection(category: e))
                            .toList(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
