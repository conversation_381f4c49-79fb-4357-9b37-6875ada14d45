import 'dart:math';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/shared/firebase.dart';
import 'package:swagat_user/shared/theme.dart';
import 'package:swagat_user/views/menupage/menupage_widgets/foodcard_bottomsheet.dart';
import '../../../models/food_model.dart';
import '../../../shared/methods.dart';
import '../../cart/add_food_sheet.dart';

class FoodCard extends StatefulWidget {
  const FoodCard({super.key, required this.food});

  final FoodModel food;

  @override
  State<FoodCard> createState() => _FoodCardState();
}

class _FoodCardState extends State<FoodCard> {
  @override
  Widget build(BuildContext context) {
    final ctrl = Get.find<HomeCtrl>();
    final crtItm = ctrl.cartItems.firstWhereOrNull((element) =>
        element.foodDocId == widget.food.docId &&
        element.variantId == widget.food.variants.first.id &&
        listsAreSame(element.addonDocIdList, widget.food.addOns));
    final favItems = ctrl.currentUserData!.favourite;
    final variant = widget.food.variants
        .firstWhereOrNull((element) => element.id == crtItm?.variantId);
    // final size = MediaQuery.sizeOf(context).width;
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 30),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
          ),
          child: const SizedBox(
            height: 150,
            child: Row(),
          ),
        ),
        /* 
        Container(
            margin: const EdgeInsets.only(top: 38),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
            ),
            child: const SizedBox(
              height: 150,
              child: Row(),
            )),
        */
        InkWell(
          onTap: () {
            showDragableSheet(
              context,
              showDragHandle: false,
              BottomSheetFoodCard(foodDocId: widget.food.docId),
            );
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  // width: size / 2.5,
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Hero(
                    tag: widget.food.docId,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(18),
                        child: CachedNetworkImage(
                          imageUrl: widget.food.imageUrl,
                          // height: size / 2.5,
                          // width: size / 2.5,
                          // height: 135,
                          // width: 125,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Text(
                    widget.food.name.capitalize!,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    style: const TextStyle(
                        fontSize: 18, overflow: TextOverflow.ellipsis),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Text(
                    '₹ ${widget.food.variants.map((e) => e.price).reduce(min)}',
                    style: const TextStyle(
                        fontWeight: FontWeight.w600, fontSize: 17),
                  ),
                ),
                crtItm != null &&
                        (widget.food.variants.length == 1 &&
                            widget.food.addOns.isEmpty)
                    ? Container(
                        width: 120,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            IconButton(
                              style: IconButton.styleFrom(
                                elevation: 0,
                                backgroundColor: themeColor,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(6)),
                              ),
                              onPressed: () {
                                ctrl.removeFromCart(
                                  widget.food.docId,
                                  variant!.id,
                                );
                              },
                              icon: const Icon(
                                CupertinoIcons.minus,
                                size: 18,
                                color: Colors.white,
                              ),
                            ),
                            Expanded(
                              child: Container(
                                color: Colors.white,
                                child: Text(
                                  crtItm.qty.toString(),
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(
                                    fontSize: 17,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                            IconButton(
                              style: IconButton.styleFrom(
                                elevation: 0,
                                backgroundColor: themeColor,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(6)),
                              ),
                              onPressed: () {
                                if (!widget.food.availableNow) return;
                                ctrl.addToCart(
                                  widget.food.docId,
                                  variant!.id,
                                  [],
                                  userCartItem: crtItm,
                                );
                              },
                              icon: const Icon(
                                CupertinoIcons.add,
                                size: 20,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          InkWell(
                            onTap: () {
                              favItems.contains(widget.food.docId)
                                  ? FBFireStore.users
                                      .doc(FBAuth.auth.currentUser?.uid)
                                      .update(
                                      {
                                        "favourite": FieldValue.arrayRemove(
                                            [widget.food.docId])
                                      },
                                    )
                                  : FBFireStore.users
                                      .doc(FBAuth.auth.currentUser?.uid)
                                      .update(
                                      {
                                        "favourite": FieldValue.arrayUnion(
                                            [widget.food.docId])
                                      },
                                    );
                            },
                            child: favItems.contains(widget.food.docId)
                                ? const Icon(
                                    CupertinoIcons.heart_fill,
                                    size: 32,
                                    color: Colors.red,
                                  )
                                : const Icon(
                                    CupertinoIcons.suit_heart,
                                    size: 32,
                                  ),
                          ),
                          ElevatedButton.icon(
                            onPressed: () {
                              if (widget.food.variants.length == 1 &&
                                  widget.food.addOns.isEmpty) {
                                if (crtItm == null) {
                                  if (widget.food.availableNow) {
                                    ctrl.addToCart(widget.food.docId,
                                        widget.food.variants.first.id, [],
                                        userCartItem: crtItm);
                                  }
                                }
                                ctrl.update();
                              } else {
                                showModalBottomSheet(
                                    useRootNavigator: true,
                                    showDragHandle: true,
                                    context: context,
                                    builder: (context) => AddToCartSheet(
                                        foodDocId: widget.food.docId));
                              }
                            },
                            icon: const Icon(CupertinoIcons.add,
                                size: 20, color: Color(0xffd47c85)),
                            label: const Text("Add",
                                style: TextStyle(fontSize: 18)),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xfffff6f7),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 0,
                              shadowColor: Colors.transparent,
                              side: const BorderSide(
                                  color: Color(0xffd47c85), width: .9),
                            ),
                          )
                        ],
                      ),
                const SizedBox(height: 8)
              ],
            ),
          ),
        ),
      ],
    );
  }
}
