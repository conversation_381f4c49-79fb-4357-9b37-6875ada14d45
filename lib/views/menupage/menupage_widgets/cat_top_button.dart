// ignore_for_file: no_wildcard_variable_uses

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/models/categories_model.dart';

class CategoriesTab extends StatelessWidget {
  const CategoriesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: GetBuilder<HomeCtrl>(
        builder: (ctrl) {
          return Row(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  // padding: const EdgeInsets.only(left: 14),
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children:
                        ctrl.categories
                            .map((e) => CategoryTab(category: e))
                            .toList(),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class CategoryTab extends StatelessWidget {
  const CategoryTab({super.key, required this.category});
  final CategoryModel category;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 6, 10, 1),
      child: InkWell(
        borderRadius: BorderRadius.circular(45),
        onTap: () {
          Scrollable.ensureVisible(
            category.catKey.currentContext!,
            duration: Durations.extralong4,
            curve: Curves.decelerate,
          );
        },
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(45),
            border: Border.all(color: Colors.grey.shade400),
          ),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
          child: Text(
            category.name,
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 15,
              color: Color(0xffd47c85),
            ),
          ),
        ),
      ),

      // child: ElevatedButton(
      //   style: ElevatedButton.styleFrom(
      //     elevation: 0,
      //     shape: RoundedRectangleBorder(
      //       side: const BorderSide(
      //         color: Colors.grey,
      //         width: 1,
      //       ),0`
      //       borderRadius: BorderRadiusDirectional.circular(6),
      //     ),
      //     backgroundColor: Colors.white,
      //     padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
      //   ),
      //   onPressed: () {},
      //   child: Text(
      //     category.name,
      //     style: const TextStyle(
      //       fontWeight: FontWeight.w600,
      //       fontSize: 15,
      //     ),
      //   ),
      // ),
    );
  }
}
