import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/models/categories_model.dart';
import '../../homepage/homepage_widegts/special_item_widget.dart';

class CategorySection extends StatefulWidget {
  const CategorySection({super.key, required this.category});
  final CategoryModel category;

  @override
  State<CategorySection> createState() => _CategorySectionState();
}

class _CategorySectionState extends State<CategorySection> {
  bool tileExpanded = false;
  @override
  Widget build(BuildContext context) {
    final ctrl = Get.find<HomeCtrl>();
    // final catFiltered = ctrl.categories
    //     .where((element) => element.name
    //         .toLowerCase()
    //         .contains(ctrl.searchController3.text.toLowerCase()))
    //     .toList();
    final filtered =
        ctrl.foods
            .where(
              (element) =>
                  element.categories.contains(widget.category.docId) &&
                  (element.name.toLowerCase().contains(
                    ctrl.searchController3.text.toLowerCase(),
                  )),
            )
            .toList();
    return filtered.isEmpty
        ? const SizedBox()
        : Padding(
          padding: const EdgeInsets.only(bottom: 15.0, left: 14, right: 14),
          child: ExpansionTile(
            key: widget.category.catKey,
            iconColor: Colors.black,
            expandedAlignment: Alignment.center,
            initiallyExpanded: true,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            onExpansionChanged: (value) => setState(() => tileExpanded = value),
            collapsedShape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            // childrenPadding: const EdgeInsets.only(bottom: 10),
            backgroundColor: Colors.white,
            collapsedBackgroundColor: Colors.white,
            trailing:
                tileExpanded
                    ? const Icon(Icons.keyboard_arrow_up_sharp)
                    : const Icon(Icons.keyboard_arrow_down_sharp),
            title: Text(
              widget.category.name,
              style: GoogleFonts.actor(
                textStyle: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
            ),
            children: [
              ListView.separated(
                padding: EdgeInsets.zero,
                itemCount: filtered.length,
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemBuilder:
                    (context, index) => SpecialItemCard(food: filtered[index]),
                separatorBuilder: (BuildContext context, int index) {
                  return const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12.0),
                    child: Divider(thickness: .2),
                  );
                },
              ),
            ],
          ),
        );
    /* 
    Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        filtered.isEmpty
            ? const SizedBox()
            : Padding(
                padding: const EdgeInsets.only(top: 15, bottom: 5),
                child: Text(
                  category.name,
                  style: const TextStyle(
                      fontSize: 25,
                      fontWeight: FontWeight.w700,
                      letterSpacing: 1.5),
                ),
              ),
        ListView.builder(
          itemCount: filtered.length,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (context, index) {
            return SpecialItemCard(food: filtered[index]);
          },
        )
      ],
    )
 */
  }
}

        // StaggeredGrid.extent(
        //   maxCrossAxisExtent: 300,
        //   crossAxisSpacing: 15,
        //   mainAxisSpacing: 15,
        //   children: filtered
        //       .map((e) => SpecialItemCard(
        //             food: e,
        //           ))
        //       .toList(),
        // ),