// ignore_for_file: no_wildcard_variable_uses

import 'package:cached_network_image/cached_network_image.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:swagat_user/models/add_on_model.dart';
import 'package:swagat_user/models/variants_model.dart';
import 'package:swagat_user/views/homepage/homepage_widegts/veg_icon.dart';
import '../../../controllers/home_controller.dart';
import '../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/theme.dart';

class BottomSheetFoodCard extends StatefulWidget {
  const BottomSheetFoodCard({super.key, required this.foodDocId});
  final String foodDocId;

  @override
  State<BottomSheetFoodCard> createState() => _BottomSheetFoodCardState();
}

class _BottomSheetFoodCardState extends State<BottomSheetFoodCard> {
  VariantModel? selectedVariant;
  List<String> selectedAddons = [];
  bool loading = false;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final food = ctrl.foods.firstWhereOrNull(
          (element) => element.docId == widget.foodDocId,
        );
        selectedVariant ??= food?.variants.first;

        final crtItm = ctrl.cartItems.firstWhereOrNull(
          (element) =>
              element.foodDocId == food?.docId &&
              element.variantId == selectedVariant?.id &&
              listsAreSame(element.addonDocIdList, selectedAddons),
        );
        final filteredAddons =
            ctrl.addOns
                .where(
                  (element) => food?.addOns.contains(element.docId) ?? false,
                )
                .toList();
        final added = crtItm != null;
        return food == null
            ? const SizedBox()
            : Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                              clipBehavior: Clip.antiAlias,
                              decoration: const BoxDecoration(
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(20),
                                  topRight: Radius.circular(20),
                                ),
                              ),
                              // image
                              child: Hero(
                                tag: food.docId,
                                child: Stack(
                                  children: [
                                    CachedNetworkImage(
                                      imageUrl: food.imageUrl,
                                      fit: BoxFit.cover,
                                      width: double.maxFinite,
                                    ),
                                    Align(
                                      alignment: Alignment.topRight,
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: IconButton.filled(
                                          style: IconButton.styleFrom(
                                            elevation: 3,
                                            backgroundColor: Colors.white,
                                            shadowColor: Colors.black54,
                                          ),
                                          onPressed: () => context.pop(),
                                          icon: const Icon(
                                            CupertinoIcons.xmark,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),

                      // favorite button
                      Padding(
                        padding: const EdgeInsets.only(
                          top: 20.0,
                          left: 15,
                          right: 15,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              food.name.capitalizeFirst ?? "",
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 24,
                              ),
                            ),
                            InkWell(
                              onTap: () async {
                                ctrl.currentUserData!.favourite.contains(
                                      food.docId,
                                    )
                                    ? await FBFireStore.users
                                        .doc(FBAuth.auth.currentUser?.uid)
                                        .update({
                                          "favourite": FieldValue.arrayRemove([
                                            food.docId,
                                          ]),
                                        })
                                    : await FBFireStore.users
                                        .doc(FBAuth.auth.currentUser?.uid)
                                        .update({
                                          "favourite": FieldValue.arrayUnion([
                                            food.docId,
                                          ]),
                                        });
                              },
                              child:
                                  ctrl.currentUserData!.favourite.contains(
                                        food.docId,
                                      )
                                      ? const Icon(
                                        CupertinoIcons.heart_fill,
                                        size: 32,
                                        color: Colors.red,
                                      )
                                      : const Icon(
                                        CupertinoIcons.heart,
                                        size: 32,
                                      ),
                            ),
                          ],
                        ),
                      ),

                      // price
                      // Padding(
                      //   padding: const EdgeInsets.only(
                      //       top: 15.0, left: 15, right: 15),
                      //   child: Text(
                      //     '₹ ${food.variants.map((e) => e.price).reduce(min)}',
                      //     style: const TextStyle(
                      //         fontWeight: FontWeight.w600, fontSize: 16),
                      //   ),
                      // ),

                      // description
                      if (food.description.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 15.0,
                            left: 15,
                            right: 15,
                          ),
                          child: SizedBox(child: Text(food.description)),
                        ),
                    ],
                  ),
                ),

                // variant list
                if (food.variants.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(
                      top: 15.0,
                      left: 15,
                      right: 15,
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(10),
                        child: Column(
                          children: [
                            const Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    'Variants',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w700,
                                      fontSize: 20,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            ListView.builder(
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: food.variants.length,
                              shrinkWrap: true,
                              itemBuilder:
                                  (context, index) => _VariantsTile(
                                    variant: food.variants[index],
                                    selectedVariant: selectedVariant,
                                    onChanged: () {
                                      selectedVariant = food.variants[index];
                                      ctrl.update();
                                    },
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                // ADD ON LIST
                if (filteredAddons.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(
                      top: 15.0,
                      left: 15,
                      right: 15,
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(10),
                        child: Column(
                          children: [
                            const Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    'Add On',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w700,
                                      fontSize: 20,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            ListView.builder(
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: filteredAddons.length,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                final addon = filteredAddons[index];
                                return _AddOnTile(
                                  addon: addon,
                                  selectedAddons: selectedAddons,
                                  onChanged: () {
                                    selectedAddons.contains(addon.docId)
                                        ? selectedAddons.remove(addon.docId)
                                        : selectedAddons.add(addon.docId);
                                    ctrl.update();
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                // add button
                Padding(
                  padding: const EdgeInsets.all(15),
                  child: IntrinsicHeight(
                    child: Row(
                      children: [
                        if (added)
                          Row(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                  color: const Color(0xfffff6f7),
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color: const Color(0xffd47c85),
                                  ),
                                ),
                                child:
                                    loading
                                        ? SizedBox(
                                          height: 50,
                                          width: 125,
                                          child: SizedBox(
                                            width: 33,
                                            height: 33,
                                            child: Center(
                                              child:
                                                  LoadingAnimationWidget.progressiveDots(
                                                    color: const Color(
                                                      0xffd47c85,
                                                    ),
                                                    size: 20,
                                                  ),
                                            ),
                                          ),
                                        )
                                        : Row(
                                          children: [
                                            IconButton(
                                              style: IconButton.styleFrom(
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                ),
                                                foregroundColor: const Color(
                                                  0xffd47c85,
                                                ),
                                              ),
                                              onPressed: () async {
                                                if (selectedVariant == null) {
                                                  return;
                                                }
                                                setState(() {
                                                  loading = true;
                                                });
                                                await ctrl.removeFromCart(
                                                  food.docId,
                                                  selectedVariant!.id,
                                                );
                                                setState(() {
                                                  loading = false;
                                                });
                                              },
                                              icon: const Icon(
                                                CupertinoIcons.minus,
                                                size: 18,
                                              ),
                                            ),
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    vertical: 10.0,
                                                  ),
                                              child: SizedBox(
                                                width: 30,
                                                child: Center(
                                                  child: Text(
                                                    crtItm.qty.toString(),
                                                    style: const TextStyle(
                                                      fontSize: 17,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            IconButton(
                                              style: ElevatedButton.styleFrom(
                                                elevation: 0,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                ),
                                                foregroundColor: const Color(
                                                  0xffd47c85,
                                                ),
                                              ),
                                              onPressed: () async {
                                                if (selectedVariant == null) {
                                                  return;
                                                }
                                                if (!food.availableNow) return;
                                                setState(() {
                                                  loading = true;
                                                });
                                                await ctrl.addToCart(
                                                  food.docId,
                                                  selectedVariant!.id,
                                                  selectedAddons,
                                                  userCartItem: crtItm,
                                                );
                                                setState(() {
                                                  loading = false;
                                                });
                                              },
                                              icon: const Icon(
                                                CupertinoIcons.add,
                                                size: 18,
                                              ),
                                            ),
                                          ],
                                        ),
                              ),
                              const SizedBox(width: 20),
                            ],
                          ),
                        Expanded(
                          child: Row(
                            children: [
                              Expanded(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    minimumSize: const Size.fromHeight(52),
                                    backgroundColor: themeColor,
                                    foregroundColor: Colors.white,
                                  ),
                                  onPressed: () {
                                    if (added) {
                                      ctrl.removeFromCart(
                                        food.docId,
                                        selectedVariant!.id,
                                        true,
                                      );
                                    } else {
                                      if (food.availableNow) {
                                        ctrl.addToCart(
                                          food.docId,
                                          selectedVariant!.id,
                                          selectedAddons,
                                          userCartItem: crtItm,
                                        );
                                      }
                                    }
                                    ctrl.update();
                                  },
                                  child: Text(
                                    added ? "Remove" : "Add to Cart",
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 18,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
      },
    );
  }
}

class _VariantsTile extends StatelessWidget {
  const _VariantsTile({
    required this.variant,
    required this.selectedVariant,
    required this.onChanged,
  });

  final VariantModel variant;
  final VariantModel? selectedVariant;
  final Function onChanged;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: InkWell(
        onTap: () => onChanged(),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                const VegIconWid(),
                const SizedBox(width: 10),
                Text(
                  variant.nameOrSize.capitalizeFirst ?? '-',
                  style: const TextStyle(fontSize: 16),
                ),
              ],
            ),
            Row(
              children: [
                Text(
                  "₹ ${variant.price.toString()}",
                  style: const TextStyle(fontSize: 16),
                ),
                SizedBox(
                  height: 20,
                  width: 35,
                  child: Padding(
                    padding: EdgeInsets.zero,
                    child: Radio<String>(
                      splashRadius: 0,
                      activeColor: themeColor,
                      fillColor: WidgetStateProperty.resolveWith<Color>((
                        Set<WidgetState> states,
                      ) {
                        if (states.contains(WidgetState.selected)) {
                          return themeColor;
                        }
                        return themeColor;
                      }),
                      value: variant.id,
                      onChanged: (newValue) => onChanged(),
                      groupValue: selectedVariant?.id,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _AddOnTile extends StatelessWidget {
  const _AddOnTile({
    required this.addon,
    required this.selectedAddons,
    required this.onChanged,
  });
  final AddOnModel addon;
  final List<String> selectedAddons;
  final Function onChanged;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 15.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              const VegIconWid(),
              const SizedBox(width: 10),
              Text(
                addon.name.capitalizeFirst ?? "-",
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
          Row(
            children: [
              Text(
                "₹ ${addon.price.toString()}",
                style: const TextStyle(fontSize: 16),
              ),
              SizedBox(
                height: 20,
                width: 35,
                child: Padding(
                  padding: EdgeInsets.zero,
                  child: Checkbox(
                    splashRadius: 0,
                    activeColor: themeColor,
                    value: selectedAddons.contains(addon.docId),
                    side: const BorderSide(color: themeColor, width: 2),
                    onChanged: (value) => onChanged(),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
