// ignore_for_file: no_wildcard_variable_uses

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/home_controller.dart';
import '../../shared/theme.dart';

class NavbarTile extends StatelessWidget {
  const NavbarTile({
    super.key,
    required this.icon,
    required this.textName,
    required this.selectedIndex,
  });

  final IconData icon;
  final String textName;
  final int selectedIndex;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final selected = ctrl.selectedIndex == selectedIndex;

        return InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            ctrl.selectedIndex = selectedIndex;
            ctrl.update();
          },
          child: Container(
            decoration: BoxDecoration(
              // boxShadow: [BoxShadow(color: Colors.grey, blurRadius: 2)],
              // color: selected ? Colors.white : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _icon(selected),
                const SizedBox(height: 5),
                _text(selected),
              ],
            ),
          ),
        );
      },
    );
  }

  Icon _icon(bool selected) =>
      Icon(icon, size: selected ? 24 : 22, color: selected ? themeColor : null);

  Text _text(bool selected) {
    return Text(
      textName,
      style: TextStyle(
        fontWeight: selected ? FontWeight.w600 : FontWeight.w500,
        color: selected ? themeColor : null,
        fontSize: 14,
      ),
    );
  }
}
/*
        child: Row(
          children: [
            Icon(icon),
            SizedBox(width: 15),
            Text(textName),
          ],
        ),
*/