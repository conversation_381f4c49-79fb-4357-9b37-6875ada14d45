// ignore_for_file: no_wildcard_variable_uses

import 'dart:math';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:map_location_picker/map_location_picker.dart';
import 'package:swagat_user/controllers/auth_controller.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/models/address_model.dart';
import 'package:swagat_user/models/discount_coupon.dart';
import 'package:swagat_user/models/food_model.dart';
import 'package:swagat_user/models/user_model.dart';
import 'package:swagat_user/shared/cashfree_payment.dart';
import 'package:swagat_user/shared/const.dart';
import 'package:swagat_user/shared/methods.dart';
import 'package:swagat_user/shared/router.dart';
import 'package:swagat_user/shared/theme.dart';
import 'package:swagat_user/views/homepage/homepage_widegts/veg_icon.dart';
import '../../shared/firebase.dart';
import '../account/widgets/adress_page.dart';
import 'total_bill_sheet.dart';

class CartPage extends StatefulWidget {
  const CartPage({super.key});

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  bool loading = false;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        ctrl.cartItems.sort((a, b) => a.foodDocId.compareTo(b.foodDocId));
        return Scaffold(
          backgroundColor: const Color(0xfff5f6fb),
          appBar: AppBar(
            titleSpacing: 0,
            centerTitle: false,
            shadowColor: Colors.black38,
            elevation: 5,
            scrolledUnderElevation: 5,
            surfaceTintColor: Colors.white,
            automaticallyImplyLeading: true,
            // leading: const Icon(CupertinoIcons.chevron_back),
            title: const Text("Cart Page", style: TextStyle(fontSize: 20)),
          ),
          body: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child:
                ctrl.cartItems.isEmpty
                    ? const _NoItemSelectedWid()
                    : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 18),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 15),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(13),
                                  color: Colors.white,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(15.0),
                                  child: ListView.builder(
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    itemCount: ctrl.cartItems.length,
                                    itemBuilder: (context, index) {
                                      final food = ctrl.foods.firstWhereOrNull(
                                        (element) =>
                                            element.docId ==
                                            ctrl.cartItems[index].foodDocId,
                                      );
                                      return food == null
                                          ? const SizedBox()
                                          : CartItemTile(
                                            crim: ctrl.cartItems[index],
                                            food: food,
                                          );
                                    },
                                  ),
                                ),
                              ),
                            ),
                            if (ctrl.rewardPointsEarned > 0)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 20.0),
                                child: Column(
                                  children: [
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Expanded(
                                          child: Image.asset(
                                            'assets/reward.png',
                                          ),
                                        ),
                                        Expanded(
                                          flex: 2,
                                          child: Column(
                                            children: [
                                              Text(
                                                "Get ${ctrl.currentOutlet?.rewardPoints ?? 0}% of item total as reward points. Auto appplied on your next order",
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 0),
                                    Text.rich(
                                      TextSpan(
                                        text: "You will earn ",
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                        children: [
                                          TextSpan(
                                            text: ctrl.rewardPointsEarned
                                                .toStringAsFixed(2),
                                            style: const TextStyle(
                                              color: Colors.blue,
                                            ),
                                          ),
                                          const TextSpan(
                                            text: " points on this orders",
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                            if (ctrl.coupons.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.only(bottom: 15),
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(13),
                                    color: Colors.white,
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(15.0),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Padding(
                                          padding: EdgeInsets.only(
                                            bottom: 18.0,
                                          ),
                                          child: Row(
                                            children: [
                                              Icon(
                                                CupertinoIcons.ticket,
                                                size: 20,
                                              ),
                                              SizedBox(width: 8),
                                              Text(
                                                "Coupons",
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 16,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        ListView.separated(
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          shrinkWrap: true,
                                          itemCount: ctrl.coupons.length,
                                          itemBuilder: (context, index) {
                                            return _Coupontile(
                                              ctrl.coupons[index],
                                              ctrl.coupons[index].docId ==
                                                  ctrl.selectedCoupon?.docId,
                                            );
                                          },
                                          separatorBuilder: (
                                            BuildContext context,
                                            int index,
                                          ) {
                                            return const Divider(thickness: .3);
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),

                            const DeliveryTakeAwaySelector(),

                            // if (ctrl.selectedAddress != null)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 15),
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(13),
                                  color: Colors.white,
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 15,
                                        vertical: 8,
                                      ),
                                      child: Column(
                                        children: [
                                          if (ctrl.selectedOrderType ==
                                              OrderType.delivery)
                                            InkWell(
                                              onTap: () {
                                                showModalBottomSheet(
                                                  backgroundColor:
                                                      Colors.grey[200],
                                                  useRootNavigator: true,
                                                  isScrollControlled: true,
                                                  context: context,
                                                  builder: (context) {
                                                    return const AddressSheet();
                                                  },
                                                );
                                              },
                                              child:
                                                  ctrl.selectedAddress == null
                                                      ? _CartTile(
                                                        icon:
                                                            Icons.location_pin,
                                                        title: Text(
                                                          "Select Address",
                                                        ),
                                                        arrow: true,
                                                        divider: false,
                                                      )
                                                      : _CartTile(
                                                        icon:
                                                            Icons
                                                                .location_on_outlined,
                                                        title: Column(
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            const Text(
                                                              "Delivery Address",
                                                            ),
                                                            Text(
                                                              ctrl
                                                                  .selectedAddress!
                                                                  .addressType,
                                                            ),
                                                            Text(
                                                              "${ctrl.selectedAddress!.flat}, ${ctrl.selectedAddress!.area} ",
                                                            ),
                                                            Text(
                                                              "${ctrl.selectedAddress!.city},",
                                                            ),
                                                            // Text(ctrl.currentUserData!
                                                            //         .defaultAddressId ??
                                                            //     "-"),
                                                          ],
                                                        ),
                                                        arrow: true,
                                                        divider: true,
                                                      ),
                                            ),
                                          if (ctrl.selectedAddress != null)
                                            _CartTile(
                                              icon: CupertinoIcons.phone,
                                              title: Text(
                                                "${ctrl.selectedAddress!.name}, ${ctrl.selectedAddress!.contact}",
                                              ),
                                              arrow: true,
                                              divider: true,
                                            ),
                                          if (ctrl.selectedAddress != null)
                                            InkWell(
                                              onTap: () {
                                                showModalBottomSheet(
                                                  context: context,
                                                  builder:
                                                      (
                                                        context,
                                                      ) => ZomatoBottomSheet(
                                                        itemTotal:
                                                            ctrl.subTotalBeforeDis,
                                                        discount:
                                                            ctrl.discountAmount -
                                                            ctrl.rewardPointsUsed,
                                                        gst: ctrl.totalTax,
                                                        grandTotal:
                                                            ctrl.grandTotal,
                                                        rewardPointsApplied:
                                                            ctrl.rewardPointsUsed,
                                                      ),
                                                );
                                              },
                                              child: _CartTile(
                                                icon:
                                                    CupertinoIcons
                                                        .doc_checkmark,
                                                title: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text.rich(
                                                      TextSpan(
                                                        text: "Total Bill ",
                                                        children: [
                                                          TextSpan(
                                                            text:
                                                                "₹${ctrl.grandTotal.toStringAsFixed(2)}",
                                                            style:
                                                                const TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    Wrap(
                                                      spacing: 8,
                                                      children: [
                                                        Text(
                                                          "Auto Applied Reward Points",
                                                          style: TextStyle(
                                                            color:
                                                                Colors
                                                                    .grey
                                                                    .shade700,
                                                          ),
                                                        ),
                                                        Container(
                                                          padding:
                                                              const EdgeInsets.fromLTRB(
                                                                3,
                                                                1,
                                                                6,
                                                                1,
                                                              ),
                                                          decoration: BoxDecoration(
                                                            color: Colors.green
                                                                .withOpacity(
                                                                  .1,
                                                                ),
                                                            borderRadius:
                                                                BorderRadius.circular(
                                                                  45,
                                                                ),
                                                          ),
                                                          child: Text(
                                                            " -₹${ctrl.rewardPointsUsed.toStringAsFixed(2)}",
                                                            style:
                                                                const TextStyle(
                                                                  color:
                                                                      Colors
                                                                          .green,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                                arrow: true,
                                                divider: false,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            // Display order note input field
                            Container(
                              margin: const EdgeInsets.only(bottom: 12, top: 8),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  /* const SizedBox(height: 12),
                                const Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 8.0),
                                  child: Text(
                                    "Payment Mode",
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                ),
                                const Divider(
                                  indent: 8,
                                  endIndent: 8,
                                ), */
                                  if (ctrl.currentOutlet?.cashOnDelivery ??
                                      false)
                                    RadioListTile(
                                      value: true,
                                      title: const Text("Cash on Delivery"),
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 0,
                                      ),

                                      groupValue: ctrl.cod,
                                      onChanged: (value) {
                                        ctrl.cod = true;
                                        ctrl.update();
                                      },
                                    ),
                                  RadioListTile(
                                    value: false,
                                    title: const Text("Pay Online"),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 0,
                                    ),

                                    groupValue: ctrl.cod,
                                    onChanged: (value) {
                                      ctrl.cod = false;
                                      ctrl.update();
                                    },
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              margin: const EdgeInsets.only(bottom: 20, top: 8),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: InkWell(
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () {
                                  showDialog(
                                    context: context,
                                    builder: (context) {
                                      return Dialog(
                                        backgroundColor: Color.fromARGB(
                                          255,
                                          241,
                                          241,
                                          241,
                                        ),
                                        surfaceTintColor: Color.fromARGB(
                                          255,
                                          236,
                                          236,
                                          236,
                                        ),

                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 15.0,
                                            horizontal: 15,
                                          ),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              const Text(
                                                "Order Note",

                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 18,
                                                ),
                                              ),
                                              SizedBox(height: 13),

                                              TextField(
                                                controller: ctrl.orderNoteCtrl,
                                                decoration: InputDecoration(
                                                  fillColor: Colors.white,
                                                  filled: true,
                                                  border: OutlineInputBorder(
                                                    borderSide: BorderSide.none,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          8,
                                                        ),
                                                  ),
                                                  enabledBorder:
                                                      OutlineInputBorder(
                                                        borderSide:
                                                            BorderSide.none,
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              8,
                                                            ),
                                                      ),
                                                  focusedBorder:
                                                      OutlineInputBorder(
                                                        borderSide:
                                                            BorderSide.none,
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              8,
                                                            ),
                                                      ),

                                                  hintText: 'Add Order Note',
                                                ),
                                                maxLines:
                                                    4, // Allow multiline input
                                                // onChanged: (value) {},
                                              ),
                                              SizedBox(height: 13),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.end,
                                                children: [
                                                  TextButton(
                                                    child: const Text("Cancel"),
                                                    onPressed: () {
                                                      Navigator.of(
                                                        context,
                                                      ).pop();
                                                    },
                                                  ),
                                                  SizedBox(width: 10),
                                                  ElevatedButton(
                                                    style:
                                                        ElevatedButton.styleFrom(
                                                          backgroundColor:
                                                              themeColor,
                                                          foregroundColor:
                                                              Colors.white,
                                                        ),
                                                    child: const Text("Save"),
                                                    onPressed: () {
                                                      Navigator.of(
                                                        context,
                                                      ).pop();
                                                    },
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  );
                                },
                                child: TextField(
                                  controller: ctrl.orderNoteCtrl,
                                  style: TextStyle(
                                    color: Color.fromARGB(255, 50, 50, 50),
                                  ),
                                  decoration: const InputDecoration(
                                    enabled: false,
                                    labelStyle: TextStyle(
                                      color: Color.fromARGB(255, 50, 50, 50),
                                    ),

                                    hintText: 'Add Order Note',
                                    border: InputBorder.none,
                                    labelText: "Order Note",
                                  ),
                                  maxLines: null, // Allow multiline input
                                  onChanged: (value) {
                                    // Update order note in your data model
                                    // For example, you can use setState() to update a variable in the parent widget
                                  },
                                ),
                              ),
                            ),
                            const SizedBox(height: 20),
                          ],
                        ),
                      ),
                    ),
          ),
          bottomNavigationBar:
              ctrl.cartItems.isNotEmpty
                  ? Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(color: Colors.black12, blurRadius: 8),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(18.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: GetBuilder<HomeCtrl>(
                              builder: (ctrl) {
                                return Text(
                                  "Total amount : ₹${ctrl.grandTotal.toStringAsFixed(2)} (Incl. tax)",
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 15,
                                  ),
                                );
                              },
                            ),
                          ),
                          const SizedBox(width: 30),
                          Expanded(
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                fixedSize: const Size.fromHeight(48),
                                shadowColor: Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                backgroundColor: const Color(0xfff04f5f),
                                foregroundColor: const Color(0xfffff2f6),
                              ),
                              onPressed: () async {
                                if (loading) return;
                                try {
                                  // pgTest();
                                  // return;
                                  if (ctrl.currentUserData == null ||
                                      ctrl.selectedAddress == null ||
                                      ctrl.currentOutlet == null ||
                                      !ctrl.isOutletActive) {
                                    return;
                                  }
                                  final deliveryDistance = getDistanceBetween(
                                    LatLng(
                                      ctrl.currentOutlet?.lat.toDouble() ?? 0,
                                      ctrl.currentOutlet?.long.toDouble() ?? 0,
                                    ),
                                    LatLng(
                                      ctrl.selectedAddress?.lat.toDouble() ?? 0,
                                      ctrl.selectedAddress?.long.toDouble() ??
                                          0,
                                    ),
                                  );
                                  if (deliveryDistance >
                                      ctrl.currentOutlet!.radius) {
                                    showDialog(
                                      context: context,
                                      builder:
                                          (context) => const OutOfRangeDialog(),
                                    );
                                    return;
                                  }
                                  setState(() => loading = true);
                                  final currentOutletDocId =
                                      ctrl.currentOutlet?.docId ?? "";
                                  final currentUserDocId =
                                      FBAuth.auth.currentUser?.uid;
                                  final grandTotal = ctrl.grandTotal;
                                  final totalTax = ctrl.totalTax;
                                  final selectedOrderType =
                                      ctrl.selectedOrderType;
                                  final currentUserData = ctrl.currentUserData;
                                  final selectedAddress = ctrl.selectedAddress;
                                  final discountAmount = ctrl.discountAmount;
                                  final rewardPointsUsed =
                                      ctrl.rewardPointsUsed;
                                  final rewardPointsEarned =
                                      ctrl.rewardPointsEarned;
                                  final cod = ctrl.cod;
                                  final selectedCoupon = ctrl.selectedCoupon;
                                  Map<String, dynamic> data = {};
                                  final finalPayment = double.parse(
                                    grandTotal.toStringAsFixed(2),
                                  );
                                  data['orderId'] = '';
                                  data['uId'] = currentUserDocId;
                                  data['time'] = FieldValue.serverTimestamp();
                                  data['outletDocId'] = currentOutletDocId;
                                  data['type'] = selectedOrderType;
                                  data['totalAmountPaid'] = finalPayment;
                                  data['totalTax'] = totalTax;
                                  data['isPaid'] = false;
                                  data['status'] = OrderStatus.pending;
                                  data['deliveredOn'] = null;
                                  data['takeAwayOn'] = null;
                                  data['userData'] = {
                                    'docId': currentUserData?.docId,
                                    'name': currentUserData?.name,
                                    'number': currentUserData?.number,
                                    // 'address': selAddress?.toJson(),
                                  };
                                  data['userAddress'] =
                                      selectedAddress!.toJson();
                                  data['rider'] = null;
                                  data['isCompleted'] = false;
                                  data['transId'] = "";
                                  data['discountAmount'] = discountAmount;
                                  data['rewardPointsUsed'] = rewardPointsUsed;
                                  data['rewardPointsEarned'] =
                                      rewardPointsEarned;
                                  data['otp'] = Random().nextInt(9000) + 1000;
                                  data['cashOnDelivery'] = cod;
                                  data['orderNote'] = ctrl.orderNoteCtrl.text;
                                  data['disCoupon'] = selectedCoupon?.toJson();
                                  Map<String, dynamic> fditms = {};
                                  for (var element in ctrl.cartItems) {
                                    final foodModel = ctrl.foods
                                        .firstWhereOrNull(
                                          (e) => e.docId == element.foodDocId,
                                        );
                                    final variantModel = foodModel?.variants
                                        .firstWhereOrNull(
                                          (ele) => ele.id == element.variantId,
                                        );
                                    Map<String, dynamic> addonData = {};
                                    for (var adn in element.addonDocIdList) {
                                      final adnModel = ctrl.addOns
                                          .firstWhereOrNull(
                                            (e) => e.docId == adn,
                                          );
                                      if (adnModel != null) {
                                        addonData.addAll({
                                          adnModel.docId: {
                                            'name': adnModel.name,
                                            'price': adnModel.price,
                                            'tax': adnModel.tax,
                                          },
                                        });
                                      }
                                    }
                                    fditms.addAll({
                                      getRandomId(6): {
                                        'foodDocId': element.foodDocId,
                                        'name': foodModel?.name,
                                        'variantName': variantModel?.nameOrSize,
                                        'variantId': element.variantId,
                                        'status': true,
                                        'tax': foodModel?.taxPercentage,
                                        'price': variantModel?.price,
                                        'qty': element.qty,
                                        'addons': addonData,
                                      },
                                    });
                                  }
                                  data['foodItems'] = fditms;
                                  final oData = await FBFireStore.orders.add(
                                    data,
                                  );

                                  if (!cod) {
                                    bool res = await PaymentGatewayClass()
                                        .createCashfreeOrderPayment(
                                          orderId: oData.id,
                                          receiptNo: oData.id,
                                          amount: finalPayment,
                                        );
                                    if (res) {
                                      final orderId = await getOrderId(
                                        currentOutletDocId,
                                      );
                                      await FBFireStore.fb.runTransaction((
                                        transaction,
                                      ) async {
                                        // GET USER DATA
                                        final userData = await transaction.get(
                                          FBFireStore.users.doc(
                                            FBAuth.auth.currentUser?.uid,
                                          ),
                                        );
                                        final oldRewardPoints =
                                            userData.data()?['rewardPoints'];
                                        // CREATE ORDER

                                        transaction.update(
                                          FBFireStore.orders.doc(oData.id),
                                          {'orderId': orderId},
                                        );
                                        // transaction.set(
                                        //   FBFireStore.orders.doc(),
                                        //   data,
                                        // );
                                        // UPDATE USER DATA
                                        transaction.update(
                                          FBFireStore.users.doc(
                                            FBAuth.auth.currentUser?.uid,
                                          ),
                                          {
                                            'rewardPoints':
                                                oldRewardPoints -
                                                rewardPointsUsed +
                                                rewardPointsEarned,
                                          },
                                        );
                                      });
                                      if (mounted) {
                                        showAppSnackBar("Order Placed");
                                        // CLEAR FIELDS AND CART
                                        // ctrl.clearCart();
                                        ctrl.orderNoteCtrl.clear();

                                        setState(() => loading = false);
                                        // context.pop();
                                        // await Future.delayed(
                                        //   Duration(milliseconds: 100),
                                        // );
                                        if (context.mounted) {
                                          playOrderConfirmationSound();
                                          showDialog(
                                            barrierDismissible: false,
                                            context: context,
                                            builder: (context) {
                                              return true
                                                  ? OrderConfirmDialog(
                                                    orderId: orderId!,
                                                    oData: oData,
                                                  )
                                                  : AlertDialog(
                                                    title: const Text(
                                                      "Order Placed",
                                                    ),
                                                    content: const Text(
                                                      "Your order has been placed successfully",
                                                    ),
                                                    actions: [
                                                      TextButton(
                                                        onPressed: () {
                                                          Navigator.pop(
                                                            context,
                                                          );
                                                          context.push(
                                                            '${Routes.liveorder}/${oData.id}',
                                                          );
                                                        },
                                                        child: const Text(
                                                          "See Details",
                                                        ),
                                                      ),
                                                    ],
                                                  );
                                            },
                                          );
                                        }
                                      }
                                    } else {
                                      await FBFireStore.orders
                                          .doc(oData.id)
                                          .delete();
                                      if (mounted) {
                                        setState(() => loading = false);
                                        showAppSnackBar("Payment Failed");
                                      }
                                    }
                                  }
                                  // await FBFireStore.fb.runTransaction((
                                  //   transaction,
                                  // ) async {
                                  //   // GET USER DATA
                                  //   final userData = await transaction.get(
                                  //     FBFireStore.users.doc(
                                  //       FBAuth.auth.currentUser?.uid,
                                  //     ),
                                  //   );
                                  //   final oldRewardPoints =
                                  //       userData.data()?['rewardPoints'];
                                  //   // CREATE ORDER
                                  //   transaction.set(
                                  //     FBFireStore.orders.doc(),
                                  //     data,
                                  //   );
                                  //   // UPDATE USER DATA
                                  //   transaction.update(
                                  //     FBFireStore.users.doc(
                                  //       FBAuth.auth.currentUser?.uid,
                                  //     ),
                                  //     {
                                  //       'rewardPoints':
                                  //           oldRewardPoints -
                                  //           rewardPointsUsed +
                                  //           rewardPointsEarned,
                                  //     },
                                  //   );
                                  // });
                                  // showAppSnackBar("Order Placed");
                                  else {
                                    // CLEAR FIELDS AND CART
                                    final orderId = await getOrderId(
                                      currentOutletDocId,
                                    );
                                    await FBFireStore.orders
                                        .doc(oData.id)
                                        .update({'orderId': orderId});
                                    // SAVE DEFAULT ADDreSS IF NULL
                                    if (currentUserData!.defaultAddressId ==
                                        null) {
                                      await FBFireStore.users
                                          .doc(currentUserData.docId)
                                          .update({
                                            'defaultAddressId':
                                                selectedAddress.id,
                                          });
                                    }
                                    if (mounted) {
                                      ctrl.clearCart();
                                      ctrl.orderNoteCtrl.clear();
                                      // context.go(
                                      //   '${Routes.liveorder}/${oData.id}',
                                      // );
                                      if (context.mounted) {
                                        playOrderConfirmationSound();
                                        showDialog(
                                          context: context,
                                          builder: (context) {
                                            return OrderConfirmDialog(
                                              orderId: orderId!,
                                              oData: oData,
                                            );
                                          },
                                        );
                                      }
                                      setState(() => loading = false);
                                    }
                                  }
                                } catch (e) {
                                  debugPrint(e.toString());
                                  setState(() => loading = false);
                                }
                              },
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 1.0,
                                ),
                                child:
                                    loading
                                        ? const SizedBox(
                                          height: 30,
                                          width: 30,
                                          child: CircularProgressIndicator(
                                            color: Colors.white,
                                          ),
                                        )
                                        : const Text(
                                          "Place Order",
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            fontSize: 17,
                                          ),
                                        ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                  : const SizedBox(),
        );
      },
    );
  }

  pgTest() async {
    try {
      var client = http.Client();
      try {
        final submwrchantId = "45"; // getRandomId(12);
        final referenceNo = "123abc"; // getRandomId(20);
        const amount = "10";
        final queryParams = <String, dynamic>{
          "merchantid": "139046",
          "mandatory fields": aesEncript("$referenceNo|$submwrchantId|$amount"),
          "optional fields": "",
          "returnurl": aesEncript(
            "https://paymentcallback-22yvdcd5ra-uc.a.run.app",
          ),
          "Reference No": aesEncript(referenceNo),
          "submerchantid": aesEncript(submwrchantId),
          "transaction amount": aesEncript(amount),
          "paymode": aesEncript("9"),
        };
        final queryParams2 = <String, dynamic>{
          "merchantid": "139046",
          "mandatory%20fields": "$referenceNo|$submwrchantId|$amount",
          "optional%20fields": "",
          "returnurl": "https://paymentcallback-22yvdcd5ra-uc.a.run.app/",
          "Reference%20No": referenceNo,
          "submerchantid": submwrchantId,
          "transaction%20amount": amount,
          "paymode": "9",
        };
        var response = await client.get(
          Uri.https('eazypayuat.icicibank.com', 'EazyPG', queryParams),
        );
        // print(response.body);
        Clipboard.setData(ClipboardData(text: response.body.toString()));
        // var decodedResponse =
        //     jsonDecode(utf8.decode(response.bodyBytes)) as Map;
        // print(decodedResponse);
      } finally {
        client.close();
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}

class OrderConfirmDialog extends StatefulWidget {
  const OrderConfirmDialog({
    super.key,
    required this.oData,
    required this.orderId,
  });

  final DocumentReference<Map<String, dynamic>> oData;
  final String orderId;

  @override
  State<OrderConfirmDialog> createState() => _OrderConfirmDialogState();
}

class _OrderConfirmDialogState extends State<OrderConfirmDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    )..repeat(reverse: true); // Repeats the animation

    _animation = Tween<double>(
      begin: 0.9,
      end: 1.2,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      // insetPadding: EdgeInsets.only(),
      child: Container(
        padding: const EdgeInsets.all(20),
        constraints: BoxConstraints(maxWidth: 400, maxHeight: 500),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ScaleTransition(
              scale: _animation,
              child: Image.asset(
                'assets/order_tick.png', // Replace with your image
                width: 130,
                height: 130,
              ),
            ),

            const SizedBox(height: 20),
            const Text(
              'Order Placed',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 10),
            Text(
              'Your order has been placed successfully under id #${widget.orderId}.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                elevation: 0,
                backgroundColor: themeColor,

                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                Navigator.pop(context);
                context.push('${Routes.liveorder}/${widget.oData.id}');
              },
              child: const Text(
                "Track Order",
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _NoItemSelectedWid extends StatelessWidget {
  const _NoItemSelectedWid();

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Padding(
            padding: EdgeInsets.only(top: 10.0),
            child: Text(
              "Your cart is empty!",
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 20, color: themeColor),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 20.0),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: themeColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              onPressed: () {
                context.pop();
                Get.find<HomeCtrl>().selectedIndex = 1;
                context.go(Routes.wrapper);
              },
              child: const Text("Go to Menu", style: TextStyle(fontSize: 18)),
            ),
          ),
        ],
      ),
    );
  }
}

class _Coupontile extends StatelessWidget {
  const _Coupontile(this.coupon, this.selected);
  final DiscountCouponModel coupon;
  final bool selected;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // icon
                // const Icon(CupertinoIcons.percent, size: 15),
                // const SizedBox(width: 10),
                // text
                Text(
                  coupon.couponCode.toUpperCase(),

                  style: TextStyle(
                    // color: Color.fromARGB(255, 54, 54, 54),
                    fontSize: 14,
                  ),
                ),
                if (selected) const SizedBox(width: 7),
                if (selected)
                  const Icon(
                    CupertinoIcons.checkmark_alt_circle,
                    color: Colors.green,
                    size: 18,
                  ),
              ],
            ),
            SizedBox(height: 5),
            Text(
              coupon.isPercentage
                  ? "*Flat ${coupon.value}% off on order above ₹${coupon.minOrderValue}"
                  : "*Flat ₹${coupon.value} off on order above ₹${coupon.minOrderValue}",
              style: TextStyle(color: Color(0xff4F4F4F), fontSize: 11.5),
            ),
          ],
        ),
        SizedBox(width: 10),
        // cupertino icon arrow
        TextButton(
          onPressed: () {
            selected
                ? Get.find<HomeCtrl>().removeCoupon(coupon)
                : Get.find<HomeCtrl>().applyCoupon(coupon);
          },
          child: Theme(
            data: ThemeData(useMaterial3: true),
            child: Text(
              selected ? "Remove" : "Apply",
              style: TextStyle(
                color: selected ? Colors.red : Colors.green,
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _CartTile extends StatelessWidget {
  const _CartTile({
    required this.icon,
    required this.title,
    required this.arrow,
    required this.divider,
  });
  final IconData icon;
  final Widget title;
  final bool arrow;
  final bool divider;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    // icon
                    Icon(icon, size: 20),
                    const SizedBox(width: 8),
                    // text
                    Expanded(child: title),
                  ],
                ),
              ),

              // cupertino icon arrow
              if (arrow) const CupertinoListTileChevron(),
            ],
          ),
        ),
        if (divider) const Divider(thickness: .3),
      ],
    );
  }
}

class CartItemTile extends StatefulWidget {
  const CartItemTile({required this.crim, required this.food, super.key});
  final UserCartItem crim;
  final FoodModel food;

  @override
  State<CartItemTile> createState() => _CartItemTileState();
}

class _CartItemTileState extends State<CartItemTile> {
  bool loading = false;

  @override
  Widget build(BuildContext context) {
    final ctrl = Get.find<HomeCtrl>();
    final variant = widget.food.variants.firstWhereOrNull(
      (element) => element.id == widget.crim.variantId,
    );
    final selAddon =
        ctrl.addOns
            .where(
              (element) => widget.crim.addonDocIdList.contains(element.docId),
            )
            .toList();
    num totalPrice = 0;
    num addOnTotal =
        selAddon.fold(0.0, (num previousValue, element) {
          return previousValue + element.price;
        }) *
        widget.crim.qty;
    if (variant != null) {
      totalPrice = ((variant.price) * widget.crim.qty) + addOnTotal;
    }
    // print("object ====== 1");
    return variant == null
        ? const SizedBox()
        : Padding(
          padding: const EdgeInsets.symmetric(vertical: 10.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(top: 4.0),
                      child: VegIconWid(),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Text(
                            widget.food.name,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 15,
                            ),
                          ),
                          // Text(
                          //   '₹ ${variant.price}',
                          //   style: const TextStyle(
                          //     fontWeight: FontWeight.w600,
                          //     fontSize: 13,
                          //   ),
                          // ),
                          Text(variant.nameOrSize.capitalizeFirst ?? ""),
                          Text(
                            selAddon
                                .map((e) => e.name.capitalizeFirst)
                                .toList()
                                .join(', '),
                            style: TextStyle(fontSize: 12),
                          ),
                          // InkWell(
                          //   child: const Text("Edit Button"),
                          //   onTap: () {},
                          // ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 10),

              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 93,
                    height: 30,
                    decoration: BoxDecoration(
                      color: const Color(0xfffff6f7),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: const Color(0xffd47c85)),
                    ),
                    child:
                        loading
                            ? Center(
                              child: LoadingAnimationWidget.progressiveDots(
                                color: const Color(0xffd47c85),
                                size: 20,
                              ),
                            )
                            : Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Align(
                                    alignment: Alignment.center,
                                    child: IconButton(
                                      style: IconButton.styleFrom(
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            6,
                                          ),
                                        ),
                                        foregroundColor: const Color(
                                          0xffd47c85,
                                        ),
                                      ),
                                      onPressed: () async {
                                        setState(() {
                                          loading = true;
                                        });
                                        await ctrl.removeFromCart(
                                          widget.food.docId,
                                          variant.id,
                                        );
                                        ctrl.update();

                                        setState(() {
                                          loading = false;
                                        });
                                        // if (crtItm!.qty > 1) {
                                        //   crtItm.qty--;
                                        // } else {
                                        //   ctrl.cartItems.remove(crtItm);
                                        // }
                                        // setState(() {});
                                      },
                                      icon: const Icon(
                                        CupertinoIcons.minus,
                                        size: 15,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: Center(
                                    child: Text(
                                      widget.crim.qty.toString(),
                                      style: const TextStyle(
                                        fontSize: 17,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(
                                  height: 30,
                                  width: 30,
                                  child: IconButton(
                                    style: ElevatedButton.styleFrom(
                                      elevation: 0,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      foregroundColor: const Color.fromARGB(
                                        255,
                                        154,
                                        115,
                                        119,
                                      ),
                                    ),
                                    onPressed: () async {
                                      setState(() {
                                        loading = true;
                                      });
                                      // if (variant.id == ctrl.boolvariantId) {
                                      //   return;
                                      // }
                                      if (!widget.food.availableNow) return;

                                      await ctrl.addToCart(
                                        widget.food.docId,
                                        variant.id,
                                        widget.crim.addonDocIdList,
                                        userCartItem: widget.crim,
                                      );
                                      ctrl.update();
                                      setState(() {
                                        loading = false;
                                      });
                                    },
                                    icon: const Icon(
                                      CupertinoIcons.add,
                                      size: 15,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 5.0),
                    child: Text(
                      "₹$totalPrice",
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 15,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
  }
}

class AddressSheet extends StatefulWidget {
  const AddressSheet({super.key});

  @override
  State<AddressSheet> createState() => _AddressSheetState();
}

class _AddressSheetState extends State<AddressSheet> {
  final searchCtrl = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.6,
      snap: true,
      snapSizes: const [.7],
      minChildSize: .5,
      expand: false,
      maxChildSize: 0.8,
      builder: (context, scrollController) {
        // final filtered = widget.outlets
        //     .where((element) => element.outletName
        //         .toLowerCase()
        //         .contains(widget.searchController.text.toLowerCase()))
        //     .toList();
        return GetBuilder<HomeCtrl>(
          builder: (ctrl) {
            final userAddress =
                searchCtrl.text.isEmpty
                    ? ctrl.currentUserData!.addresses
                    : ctrl.currentUserData!.addresses
                        .where(
                          (element) =>
                              element.flat.toLowerCase().contains(
                                searchCtrl.text.toLowerCase(),
                              ) ||
                              element.area.toLowerCase().contains(
                                searchCtrl.text.toLowerCase(),
                              ) ||
                              element.city.toLowerCase().contains(
                                searchCtrl.text.toLowerCase(),
                              ),
                        )
                        .toList();
            return SingleChildScrollView(
              controller: scrollController,
              physics: const ClampingScrollPhysics(),
              child: Container(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.sizeOf(context).height * 0.8,
                ),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white54,
                  borderRadius: BorderRadius.circular(35),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(bottom: 6.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: searchCtrl,
                              onChanged: (value) {
                                setState(() {});
                              },
                              maxLines: 1,
                              decoration: const InputDecoration(
                                hintText: 'Search',
                                hintStyle: TextStyle(color: Colors.grey),
                                suffixIcon: Icon(CupertinoIcons.search),
                                suffixIconColor: Colors.grey,
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    width: 1,
                                    color: Colors.grey,
                                    style: BorderStyle.solid,
                                  ),
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(12),
                                  ),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    width: 1,
                                    color: Colors.grey,
                                    style: BorderStyle.solid,
                                  ),
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(12),
                                  ),
                                ),
                                disabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    width: 1,
                                    color: Colors.grey,
                                    style: BorderStyle.solid,
                                  ),
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 10.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: ListView.separated(
                              itemCount: userAddress.length,
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                final address = userAddress[index];
                                return InkWell(
                                  borderRadius: BorderRadius.circular(15),
                                  onTap: () async {
                                    ctrl.selectedAddress = address;
                                    ctrl.update();
                                    if (context.mounted) {
                                      context.pop();
                                      setState(() {});
                                    }
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 10,
                                      horizontal: 8,
                                    ),
                                    decoration: BoxDecoration(
                                      // color: themeColor.withOpacity(.05),

                                      // border:
                                      // Border.all(color: themeColor),
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    child: _AddressSheetTile(address: address),
                                  ),
                                );
                              },
                              separatorBuilder: (context, index) {
                                return const SizedBox(height: 10);
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}

class _AddressSheetTile extends StatelessWidget {
  const _AddressSheetTile({required this.address});
  final AddressModel address;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 35,
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
          decoration: BoxDecoration(
            color: Colors.grey.shade400,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            address.name[0],
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.black),
          ),
        ),
        const SizedBox(width: 15),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                // "Address Type",
                address.addressType,
                style: const TextStyle(color: Colors.black, fontSize: 18),
              ),

              // Text(
              //   "Address",
              //   maxLines: 1,
              //   overflow: TextOverflow.ellipsis,
              //   style: TextStyle(
              //     color: Colors.white,
              //     fontSize: 13,
              //   ),
              // ),
              Text(
                "${address.flat}, ${address.area}, ${address.city}, ${address.state}",
                maxLines: 2,
                style: const TextStyle(
                  fontSize: 13,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () async {
            return showModalBottomSheet(
              backgroundColor: Colors.grey[200],
              useRootNavigator: true,
              isScrollControlled: true,
              context: context,
              builder: (context) {
                return AddressForm(addressModel: address);
              },
            );
          },
          icon: const Icon(Icons.edit),
        ),
      ],
    );
  }
}

class DeliveryTakeAwaySelector extends StatefulWidget {
  const DeliveryTakeAwaySelector({super.key});

  @override
  State<DeliveryTakeAwaySelector> createState() =>
      _DeliveryTakeAwaySelectorState();
}

class _DeliveryTakeAwaySelectorState extends State<DeliveryTakeAwaySelector> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final outlet = ctrl.currentOutlet;
        return outlet == null || !ctrl.isOutletActive
            ? const Center(
              child: Padding(
                padding: EdgeInsets.all(8.0),
                child: Text(
                  "Selected Outlet is not offering delivery or Takeaway for now",
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.red),
                ),
              ),
            )
            : Row(
              children: [
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    // padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      // color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child:
                        (!outlet.delivery || !outlet.takeAway)
                            ? Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                color: themeColor.withOpacity(.1),
                              ),
                              child: Text(
                                'Order Type: ${outlet.delivery ? OrderType.delivery : OrderType.takeAway}',
                                style: const TextStyle(color: themeColor),
                              ),
                            )
                            : CupertinoSlidingSegmentedControl(
                              children: {
                                if (ctrl.currentOutlet?.delivery ?? true)
                                  OrderType.delivery: const Padding(
                                    padding: EdgeInsets.all(6),
                                    child: Text(OrderType.delivery),
                                  ),
                                if (ctrl.currentOutlet?.takeAway ?? true)
                                  OrderType.takeAway: const Padding(
                                    padding: EdgeInsets.all(6),
                                    child: Text(OrderType.takeAway),
                                  ),
                              },
                              groupValue: ctrl.selectedOrderType,
                              onValueChanged: (value) {
                                ctrl.selectedOrderType = value!;
                                ctrl.update();
                                // setState(() {});
                              },
                            ),
                  ),
                ),
              ],
            );
      },
    );
  }
}

class OutOfRangeDialog extends StatelessWidget {
  const OutOfRangeDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        return AlertDialog(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          title: Text(
            "Delivery raduis is ${ctrl.currentOutlet?.radius ?? "-"} KM",
          ),
          content: Text(
            ctrl.currentOutlet?.note ??
                "Please change you delivery location. Or set a pickup location within the delivery range.",
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text("OKAY"),
            ),
          ],
        );
      },
    );
  }
}
