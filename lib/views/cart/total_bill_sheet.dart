import 'package:flutter/material.dart';

class ZomatoBottomSheet extends StatefulWidget {
  final num itemTotal;
  final num discount;
  final num gst;
  final num grandTotal;
  final num rewardPointsApplied;

  const ZomatoBottomSheet({
    super.key,
    required this.itemTotal,
    required this.discount,
    required this.gst,
    required this.grandTotal,
    required this.rewardPointsApplied,
  });

  @override
  State<ZomatoBottomSheet> createState() => _ZomatoBottomSheetState();
}

class _ZomatoBottomSheetState extends State<ZomatoBottomSheet> {
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Container(
      height: size.height * 0.3, // Adjust height as needed
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Item Total', style: TextStyle(fontSize: 16.0)),
              Text('₹ ${widget.itemTotal.toStringAsFixed(2)}',
                  style: const TextStyle(fontSize: 16.0)),
            ],
          ),
          if (widget.rewardPointsApplied > 0)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Reward Points', style: TextStyle(fontSize: 16.0)),
                Text('- ₹${widget.rewardPointsApplied.toStringAsFixed(2)}',
                    style:
                        const TextStyle(fontSize: 16.0, color: Colors.green)),
              ],
            ),
          if (widget.discount > 0)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Coupon Discount', style: TextStyle(fontSize: 16.0)),
                Text('- ₹${widget.discount.toStringAsFixed(2)}',
                    style:
                        const TextStyle(fontSize: 16.0, color: Colors.green)),
              ],
            ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('GST', style: TextStyle(fontSize: 16.0)),
              Text('₹ ${(widget.gst).toStringAsFixed(2)}',
                  style: const TextStyle(fontSize: 16.0)),
            ],
          ),
          const Divider(thickness: 1.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Grand Total',
                  style:
                      TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold)),
              Text('₹ ${(widget.grandTotal).toStringAsFixed(2)}',
                  style: const TextStyle(
                      fontSize: 18.0, fontWeight: FontWeight.bold)),
            ],
          ),
        ],
      ),
    );
  }
}
