import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:swagat_user/controllers/auth_controller.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/shared/firebase.dart';
import 'package:swagat_user/shared/router.dart';
import '../../shared/theme.dart';
import '../wrapper/outlet_selection.dart';

// TODO: Profile page need to see

class AccountScreen extends StatefulWidget {
  const AccountScreen({super.key});

  @override
  State<AccountScreen> createState() => _AccountScreenState();
}

class _AccountScreenState extends State<AccountScreen> {
  @override
  Widget build(BuildContext context) {
    final ctrl = Get.find<HomeCtrl>();
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: const Color(0xfff5f6fb),
      appBar: AppBar(
        surfaceTintColor: Colors.white,
        elevation: 10,
        shadowColor: Colors.black12,
        scrolledUnderElevation: 10,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(76),
          child: Padding(
            padding: const EdgeInsets.only(left: 13.0, right: 13, bottom: 15),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // name
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Row(
                      children: [
                        Text(
                          '${ctrl.currentUserData?.name.split(' ').map((word) => word.capitalizeFirst).join(' ')}',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 5),
                        InkWell(
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (context) {
                                final TextEditingController nameCtrl =
                                    TextEditingController(
                                      text: ctrl.currentUserData?.name ?? "",
                                    );
                                bool isLoading = false;
                                return StatefulBuilder(
                                  builder: (context, setState2) {
                                    return AlertDialog(
                                      backgroundColor: Colors.white,
                                      surfaceTintColor: Colors.white,
                                      title: const Text('Edit Name'),
                                      content: TextField(
                                        controller: nameCtrl,
                                        decoration: const InputDecoration(
                                          hintText: 'Enter your name',
                                        ),
                                      ),
                                      actions:
                                          isLoading
                                              ? [
                                                SizedBox(
                                                  height: 25,
                                                  width: 25,
                                                  child: Center(
                                                    child:
                                                        const CircularProgressIndicator(
                                                          strokeWidth: 3.5,
                                                        ),
                                                  ),
                                                ),
                                              ]
                                              : [
                                                TextButton(
                                                  onPressed:
                                                      () => context.pop(),
                                                  child: const Text('Cancel'),
                                                ),
                                                TextButton(
                                                  onPressed: () async {
                                                    if (nameCtrl.text
                                                        .trim()
                                                        .isEmpty) {
                                                      showAppSnackBar(
                                                        'Name cannot be empty',
                                                      );
                                                      return;
                                                    }
                                                    setState2(() {
                                                      isLoading = true;
                                                    });
                                                    await FBFireStore.users
                                                        .doc(
                                                          ctrl
                                                                  .currentUserData
                                                                  ?.docId ??
                                                              "",
                                                        )
                                                        .update({
                                                          'name': nameCtrl.text,
                                                        });
                                                    setState2(() {
                                                      isLoading = false;
                                                    });
                                                    context.pop();
                                                    setState(() {});
                                                  },
                                                  child: const Text('Save'),
                                                ),
                                              ],
                                    );
                                  },
                                );
                              },
                            );
                          },
                          child: const Icon(Icons.edit, size: 20),
                        ),
                      ],
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 5.0),
                      child: Text('${ctrl.currentUserData?.number}'),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 5.0),
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                          backgroundColor: Colors.red.shade50,
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                        ),
                        onPressed: () {},
                        child: Row(
                          children: [
                            const Icon(
                              CupertinoIcons.tickets,
                              size: 18,
                              color: themeColor,
                            ),
                            const SizedBox(width: 5),
                            const Text(
                              'Reward Points',
                              style: TextStyle(
                                color: themeColor,
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(width: 5),
                            Text(
                              ctrl.currentUserData?.rewardPoints
                                      .toStringAsFixed(2) ??
                                  "-",
                              style: const TextStyle(
                                color: Color.fromARGB(255, 224, 56, 72),
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                //  circle avatar
                if (ctrl.currentUserData != null &&
                    ctrl.currentUserData?.name != "")
                  InkWell(
                    onLongPress: () {
                      FirebaseMessaging.instance.subscribeToTopic('test');
                    },
                    child: CircleAvatar(
                      backgroundColor: Colors.red.withOpacity(.7),
                      radius: 25,
                      child: Text(
                        "${ctrl.currentUserData?.name[0]}".toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
      body: ListView(
        children: [
          const _Boxes(),
          const _Section1(),
          const _Section2(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 13, vertical: 20),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      shadowColor: Colors.transparent,
                      backgroundColor: Colors.white,
                      elevation: 0,
                      minimumSize: const Size.fromHeight(52),
                      splashFactory: NoSplash.splashFactory,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onPressed:
                        () => showDialog(
                          context: context,
                          builder:
                              (BuildContext context) => AlertDialog(
                                backgroundColor: Colors.white,
                                surfaceTintColor: Colors.white,
                                title: const Text('Alert'),
                                content: const Text(
                                  'Are you sure you want to logout?',
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () async {
                                      await FBAuth.auth.signOut();
                                      if (context.mounted) {
                                        context.go(Routes.signin);
                                        context.pop();
                                      }
                                      ctrl.selectedIndex = 0;
                                    },
                                    child: const Text('Yes'),
                                  ),
                                  TextButton(
                                    onPressed: () => context.pop(),
                                    child: const Text('No'),
                                  ),
                                ],
                              ),
                        ),
                    icon: const Icon(
                      CupertinoIcons.power,
                      color: themeColor,
                      size: 18,
                      opticalSize: 1,
                    ),
                    label: const Text(
                      'Logout',
                      style: TextStyle(
                        color: themeColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.fromLTRB(13, 0, 13, 25),
            child: Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () async {
                      showDialog(
                        context: context,
                        builder: (context) {
                          return AlertDialog(
                            title: Text('Alert'),
                            content: Text(
                              'Are you sure you want to delete your account?',
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => context.pop(),
                                child: Text('No'),
                              ),
                              TextButton(
                                onPressed: () async {
                                  await FBAuth.auth.currentUser?.delete();
                                  await FBAuth.auth.signOut();
                                  await FBFireStore.users
                                      .doc(ctrl.currentUserData?.docId)
                                      .delete();
                                  if (context.mounted) {
                                    Navigator.pop(context);
                                    context.go(Routes.signin);
                                    showAppSnackBar(
                                      'Account deleted successfully',
                                    );
                                  }

                                  ctrl.selectedIndex = 0;
                                },
                                child: Text('Yes'),
                              ),
                            ],
                          );
                        },
                      );
                    },
                    child: Center(
                      child: Text(
                        'Delete Account',
                        style: TextStyle(
                          color: themeColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _Boxes extends StatelessWidget {
  const _Boxes();

  @override
  Widget build(BuildContext context) {
    final ctrl = Get.find<HomeCtrl>();
    return Padding(
      padding: const EdgeInsets.only(left: 13.0, right: 13, top: 20),
      child: Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () {
                ctrl.selectedIndex = 2;
                ctrl.update();
              },
              child: Container(
                height: 110,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: Colors.white,
                ),
                child: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircleAvatar(
                      backgroundColor: Color(0xfff4f5f9),
                      radius: 20,
                      child: Icon(
                        Icons.favorite_outline_rounded,
                        color: Color(0xff8f939e),
                        size: 23,
                      ),
                    ),
                    SizedBox(height: 10),
                    Text(
                      'Favourites',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        letterSpacing: .5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: InkWell(
              onTap: () => context.push(Routes.cart),
              child: Container(
                height: 110,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: Colors.white,
                ),
                child: const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircleAvatar(
                      backgroundColor: Color(0xfff4f5f9),
                      radius: 20,
                      child: Icon(
                        CupertinoIcons.bag,
                        color: Color(0xff8f939e),
                        size: 21,
                      ),
                    ),
                    SizedBox(height: 10),
                    Text(
                      'Cart',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        letterSpacing: .5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _Section1 extends StatelessWidget {
  const _Section1();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 13.0, right: 13, top: 20),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(13),
          color: Colors.white,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 15.0, bottom: 5),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    height: 25,
                    width: 3,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(1),
                        bottomRight: Radius.circular(3),
                        topLeft: Radius.circular(1),
                        topRight: Radius.circular(3),
                      ),
                      color: Color(0xffeb5066),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    "Section 1",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w800,
                      letterSpacing: .8,
                    ),
                  ),
                ],
              ),
            ),
            _SectionTile(
              onTap: () => context.push(Routes.yourOrders),
              title: 'Your order',
              symbol: true,
              icon: Icons.file_download_done_outlined,
            ),
            _SectionTile(
              onTap: () => context.push(Routes.currOrder),
              symbol: true,
              title: 'Current Order',
              icon: Icons.fastfood_outlined,
            ),

            _SectionTile(
              onTap: () => context.push(Routes.address),
              symbol: true,
              title: 'Address Book',
              icon: Icons.bookmark_add_outlined,
            ),

            // _SectionTile(
            //   onTap: () => context.push(Routes.coupon),
            //   symbol: true,
            //   title: 'Coupons',
            //   icon: CupertinoIcons.ticket,
            // ),
            _SectionTile(
              onTap: () => context.push(Routes.search),
              title: 'Search Food',
              symbol: false,
              icon: Icons.search,
            ),
            _SectionTile(
              onTap: () {
                showModalBottomSheet(
                  backgroundColor: Colors.white,
                  useRootNavigator: true,
                  isScrollControlled: true,
                  context: context,
                  builder: (context) {
                    return const OutletSheet(fromHomePage: true);
                  },
                );
              },
              symbol: false,
              title: 'Change Outlet',
              divider: false,
              icon: Icons.location_on_outlined,
            ),
          ],
        ),
      ),
    );
  }
}

class _Section2 extends StatelessWidget {
  const _Section2();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 13.0, right: 13, top: 20),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(13),
          color: Colors.white,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 15.0, bottom: 5),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    height: 25,
                    width: 3,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(1),
                        bottomRight: Radius.circular(3),
                        topLeft: Radius.circular(1),
                        topRight: Radius.circular(3),
                      ),
                      color: Color(0xffeb5066),
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    "Section 2",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w800,
                      letterSpacing: .8,
                    ),
                  ),
                ],
              ),
            ),
            _SectionTile(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return Dialog(
                      backgroundColor: Colors.white,
                      surfaceTintColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(17),
                      ),
                      insetPadding: const EdgeInsets.all(20),
                      child: SingleChildScrollView(
                        child: Container(
                          padding: const EdgeInsets.all(18),
                          constraints: BoxConstraints(
                            maxHeight: MediaQuery.sizeOf(context).height * .5,
                            // maxWidth: MediaQuery.sizeOf(context).width * .8,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Support',
                                style: TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.w800,
                                  letterSpacing: .8,
                                ),
                              ),
                              SizedBox(height: 20),
                              ListTile(
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 2,
                                ),

                                // minLeadingWidth: 20,
                                // leading: Icon(
                                //   leadingIcon,
                                //   color: iconColor,
                                // ),
                                leading: SizedBox(
                                  height: 25,
                                  width: 25,
                                  child: Icon(CupertinoIcons.mail),
                                ),

                                title: Text(
                                  '<EMAIL>',
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(fontSize: 15),
                                ),
                                onTap: () {},
                                trailing: CupertinoListTileChevron(),

                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                  // side: BorderSide(color: Colors.grey.shade400),
                                ),
                              ),
                              // SizedBox(height: 8),
                              ListTile(
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 2,
                                ),

                                // minLeadingWidth: 20,
                                // leading: Icon(
                                //   leadingIcon,
                                //   color: iconColor,
                                // ),
                                leading: SizedBox(
                                  height: 25,
                                  width: 25,
                                  child: Icon(CupertinoIcons.phone),
                                ),

                                title: Text(
                                  '+919639639630',
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(fontSize: 15),
                                ),
                                onTap: () {},
                                trailing: CupertinoListTileChevron(),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(6),
                                  // side: BorderSide(color: Colors.grey.shade400),
                                ),
                              ),

                              SizedBox(height: 20),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
              symbol: false,
              title: 'Support',
              icon: CupertinoIcons.headphones,
            ),
            _SectionTile(
              onTap: () => context.push(Routes.aboutus),
              title: 'About Us',
              symbol: true,
              icon: Icons.file_download_done_outlined,
            ),
            _SectionTile(
              onTap: () {},
              title: 'Rate Us',
              symbol: true,
              icon: CupertinoIcons.star,
            ),
            _SectionTile(
              onTap: () => context.push(Routes.tandc),
              symbol: true,
              title: 'Policies',
              icon: Icons.add_card_outlined,
              divider: false,
            ),
          ],
        ),
      ),
    );
  }
}

class _SectionTile extends StatelessWidget {
  const _SectionTile({
    required this.title,
    required this.icon,
    required this.symbol,
    this.divider = true,
    required this.onTap,
  });
  final String title;
  final IconData icon;
  final bool symbol;
  final bool divider;
  final Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10.0),
            child: Row(
              children: [
                // icon
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 13),
                  child: CircleAvatar(
                    backgroundColor: const Color(0xfff4f5f9),
                    radius: 15,
                    child: Icon(icon, color: const Color(0xff8f939e), size: 20),
                  ),
                ),
                // title
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    letterSpacing: .5,
                  ),
                ),
                // arrow can be null
                const Spacer(),
                if (symbol)
                  const Padding(
                    padding: EdgeInsets.only(right: 12.0),
                    child: CupertinoListTileChevron(),
                  ),
              ],
            ),
          ),
        ),
        if (divider) const Divider(indent: 53, height: 0, thickness: .3),
      ],
    );
  }
}




/* 
Column(
      children: [
        Row(
          children: [
            // icon
            Padding(
              padding: EdgeInsets.all(8.0),
              child: Icon(Icons.phone),
            ),
            // title
            Text("9638211693"),
            // arrow can be null
            Spacer(),
            CupertinoListTileChevron(),
          ],
        ),
        Divider(indent: 40, height: 0),
      ],
    )
     */
    
    
    
    
    