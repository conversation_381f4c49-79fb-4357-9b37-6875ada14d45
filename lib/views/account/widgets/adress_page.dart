// ignore_for_file: no_wildcard_variable_uses

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:map_location_picker/map_location_picker.dart';
import 'package:swagat_user/controllers/auth_controller.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/models/address_model.dart';
import 'package:swagat_user/shared/methods.dart';
import 'package:swagat_user/shared/router.dart';
import '../../../shared/firebase.dart';
import '../../../shared/location_picker.dart';
import '../../../shared/theme.dart';
import '../../homepage/homepage_widegts/listheading_homepage.dart';

class AddressPage extends StatefulWidget {
  const AddressPage({super.key});

  @override
  State<AddressPage> createState() => _AddressPageState();
}

class _AddressPageState extends State<AddressPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // extendBodyBehindAppBar: true,
      backgroundColor: const Color(0xfff5f6fb),
      appBar: AppBar(
        titleSpacing: 4,
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        elevation: 10,
        scrolledUnderElevation: 10,
        shadowColor: Colors.black12,
        centerTitle: false,
        title: const Text("Address"),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: SingleChildScrollView(
          child: Column(
            children: [
              const _AddAddressTile(),
              ListHeading(title: "saved address".toUpperCase()),
              const SizedBox(height: 20),
              GetBuilder<HomeCtrl>(
                builder: (ctrl) {
                  return ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: ctrl.currentUserData!.addresses.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      final address = ctrl.currentUserData!.addresses[index];
                      return Column(
                        children: [
                          _AddressTile(
                            address: ctrl.currentUserData!.addresses[index],
                            onDelete: () {
                              return showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  bool delLoader = false;
                                  return StatefulBuilder(
                                    builder: (context, setState2) {
                                      return AlertDialog(
                                        backgroundColor: Colors.white,
                                        surfaceTintColor: Colors.white,
                                        title: const Text('Alert'),
                                        content: const Text(
                                          'Are you sure you want to delete?',
                                        ),
                                        actions:
                                            delLoader
                                                ? [
                                                  Center(
                                                    child: SizedBox(
                                                      height: 25,
                                                      width: 25,
                                                      child: const Center(
                                                        child:
                                                            CircularProgressIndicator(
                                                              strokeWidth: 2.5,
                                                            ),
                                                      ),
                                                    ),
                                                  ),
                                                ]
                                                : [
                                                  TextButton(
                                                    onPressed: () async {
                                                      if (delLoader) return;
                                                      try {
                                                        setState2(() {
                                                          delLoader = true;
                                                        });
                                                        await FBFireStore.users
                                                            .doc(
                                                              ctrl
                                                                  .currentUserData!
                                                                  .docId,
                                                            )
                                                            .update({
                                                              'addresses.${address.id}':
                                                                  FieldValue.delete(),
                                                            });
                                                        setState2(() {
                                                          delLoader = false;
                                                        });
                                                        if (context.mounted) {
                                                          context.pop();
                                                          setState(() {});
                                                        }
                                                      } on Exception catch (e) {
                                                        setState2(() {
                                                          delLoader = false;
                                                        });
                                                        context.pop();
                                                        showAppSnackBar(
                                                          'Unable to remove address',
                                                        );
                                                        debugPrint(
                                                          e.toString(),
                                                        );
                                                      }
                                                    },
                                                    child: const Text('Yes'),
                                                  ),
                                                  TextButton(
                                                    onPressed:
                                                        () => context.pop(),
                                                    child: const Text('No'),
                                                  ),
                                                ],
                                      );
                                    },
                                  );
                                },
                              );
                              //            onPressed: () => showDialog(
                              //   context: context,
                              //   builder: (BuildContext context) => AlertDialog(
                              //     title: const Text('Alert'),
                              //     content: const Text('Are you sure you want to logout?'),
                              //     actions: [
                              //       TextButton(
                              //           onPressed: () async {
                              //             await FBAuth.auth.signOut();
                              //             if (context.mounted) {
                              //               context.go(Routes.signin);
                              //               context.pop();
                              //             }
                              //           },
                              //           child: const Text('Yes')),
                              //       TextButton(
                              //           onPressed: () => context.pop(),
                              //           child: const Text('No')),
                              //     ],
                              //   ),
                              // ),
                              // FBFireStore.users
                              //     .doc(ctrl.currentUserData!.docId)
                              //     .update({
                              //   'addresses.${address.id}': FieldValue.delete()
                              // });
                              // setState(() {});
                            },
                          ),
                          const SizedBox(height: 15),
                        ],
                      );
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// ADD ADDRESS TILE
class _AddAddressTile extends StatelessWidget {
  const _AddAddressTile();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(13),
          color: Colors.white,
        ),
        child: InkWell(
          onTap: () {
            showModalBottomSheet(
              backgroundColor: Colors.grey[200],
              useRootNavigator: true,
              isScrollControlled: true,
              context: context,
              builder: (context) {
                return const AddressForm();
              },
            );
          },
          child: const Padding(
            padding: EdgeInsets.all(13),
            child: Row(
              children: [
                // icon
                Padding(
                  padding: EdgeInsets.only(right: 8),
                  child: Icon(CupertinoIcons.add, color: themeColor, size: 20),
                ),
                // title
                Text(
                  "Add Address",
                  style: TextStyle(
                    fontSize: 14,
                    color: themeColor,
                    fontWeight: FontWeight.w600,
                    letterSpacing: .5,
                  ),
                ),
                // arrow can be null
                Spacer(),
                CupertinoListTileChevron(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// ADDRESS DISPLAY TILE
class _AddressTile extends StatelessWidget {
  const _AddressTile({required this.address, required this.onDelete});
  final AddressModel address;
  final Function() onDelete;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _editSheet(context),
      child: Container(
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(13),
          color: Colors.white,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.only(top: 3.0),
              child: Icon(CupertinoIcons.home, size: 20),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    address.addressType,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 15,
                    ),
                  ),
                  const SizedBox(height: 3),
                  Text(
                    "${address.flat} ${address.area} ${address.city}, ${address.state}",
                    maxLines: 2,
                    style: const TextStyle(
                      fontSize: 13,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(height: 5),
                  Row(
                    children: [
                      InkWell(
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: const Icon(
                            Icons.edit_location_outlined,
                            size: 20,
                          ),
                        ),
                        onTap: () {
                          _editSheet(context);
                        },
                      ),
                      const SizedBox(width: 10),
                      InkWell(
                        onTap: onDelete,
                        child: Padding(
                          padding: const EdgeInsets.all(4.0),
                          child: const Icon(CupertinoIcons.delete, size: 20),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<dynamic> _editSheet(BuildContext context) {
    return showModalBottomSheet(
      backgroundColor: Colors.grey[200],
      useRootNavigator: true,
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return AddressForm(addressModel: address);
      },
    );
  }
}

// ADDRESS BOTTOM SHEET FORM
class AddressForm extends StatefulWidget {
  final AddressModel? addressModel;

  const AddressForm({super.key, this.addressModel});
  @override
  State<AddressForm> createState() => _AddressFormState();
}

class _AddressFormState extends State<AddressForm> {
  List<String> addressType = <String>["Home", "Work", "Other"];
  bool loading = false;
  TextEditingController nameCtrl = TextEditingController();
  TextEditingController locationCtrl = TextEditingController();
  SelectedLocation? selectedLocation;
  TextEditingController flatCtrl = TextEditingController();
  TextEditingController areaCtrl = TextEditingController();
  TextEditingController landmarkCtrl = TextEditingController();
  int selAddressTypeIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: const Color(0xffffffff),
      ),
      constraints: const BoxConstraints(maxWidth: 700),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20.0, horizontal: 18),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Address',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: 3.0),
                        child: Text(
                          'Please Enter Complete Address',
                          style: TextStyle(fontSize: 13),
                        ),
                      ),
                    ],
                  ),
                  IconButton.filled(
                    style: IconButton.styleFrom(
                      elevation: 3,
                      backgroundColor: Colors.white,
                      shadowColor: Colors.black54,
                    ),
                    onPressed: () => context.pop(),
                    icon: const Icon(CupertinoIcons.xmark, color: Colors.black),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              SizedBox(
                height: 47,
                child: GetBuilder<HomeCtrl>(
                  builder: (ctrl) {
                    return ListView.separated(
                      shrinkWrap: true,
                      scrollDirection: Axis.horizontal,
                      itemCount: addressType.length,
                      itemBuilder: (context, index) {
                        bool selected = selAddressTypeIndex == index;
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 5.5),
                          child: InkWell(
                            onTap: () {
                              selAddressTypeIndex = index;
                              setState(() {});
                            },
                            child: Container(
                              width: 70,
                              padding: const EdgeInsets.all(5),
                              decoration: BoxDecoration(
                                color:
                                    selected ? const Color(0xfffff6f7) : null,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  width: selected ? 1.5 : 1.1,
                                  color:
                                      selected
                                          ? const Color(0xffd99fa7)
                                          : const Color(0xffdfdfdf),
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  addressType[index],
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return const SizedBox(width: 10);
                      },
                    );
                  },
                ),
              ),
              const SizedBox(height: 15),
              StaggeredGrid.extent(
                maxCrossAxisExtent: 480,
                mainAxisSpacing: 17,
                crossAxisSpacing: 20,
                children: [
                  TextFormField(
                    controller: nameCtrl,
                    cursorColor: Colors.black,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Color(0xff3a3a3a),
                      fontSize: 14,
                    ),
                    cursorHeight: 22,
                    decoration: _textFeildDecoration().copyWith(
                      labelText: "Full name",
                    ),
                  ),
                  TextFormField(
                    controller: flatCtrl,
                    cursorColor: Colors.black,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Color(0xff3a3a3a),
                      fontSize: 14,
                    ),
                    cursorHeight: 22,
                    decoration: _textFeildDecoration().copyWith(
                      labelText: "Flat / House no / Floor / Building",
                    ),
                  ),
                  TextFormField(
                    controller: areaCtrl,
                    cursorColor: Colors.black,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Color(0xff3a3a3a),
                      fontSize: 14,
                    ),
                    cursorHeight: 22,
                    decoration: _textFeildDecoration().copyWith(
                      labelText: "Area / Locality",
                    ),
                  ),
                  TextFormField(
                    controller: landmarkCtrl,
                    cursorColor: Colors.black,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Color(0xff3a3a3a),
                      fontSize: 14,
                    ),
                    cursorHeight: 22,
                    decoration: _textFeildDecoration().copyWith(
                      labelText: "Nearby landmark (optional)",
                    ),
                  ),
                  TextFormField(
                    cursorColor: Colors.black,
                    cursorHeight: 22,

                    decoration: _textFeildDecoration().copyWith(
                      fillColor: Colors.grey[100],
                      filled: true,
                      labelText: "Vadodara",
                      labelStyle: const TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Color(0xff3a3a3a),
                        fontSize: 14,
                      ),
                      enabled: false,
                      // disabledBorder: OutlineInputBorder(
                      //   borderSide: const BorderSide(color: Color(0xfff5f6fb)),
                      //   borderRadius: BorderRadius.circular(6),
                      // ),
                    ),
                  ),
                  TextFormField(
                    cursorColor: Colors.black,
                    cursorHeight: 22,
                    decoration: _textFeildDecoration().copyWith(
                      fillColor: Colors.grey[100],
                      filled: true,
                      labelStyle: const TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Color(0xff3a3a3a),
                        fontSize: 14,
                      ),
                      labelText: "Gujarat",
                      enabled: false,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 15),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      style: OutlinedButton.styleFrom(
                        minimumSize: const Size.fromHeight(52),
                        side: const BorderSide(color: Color(0xffdfdfdf)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      icon: const Icon(CupertinoIcons.placemark),
                      onPressed: () async {
                        final resp = await context.push<SelectedLocation?>(
                          Routes.location,
                        );
                        selectedLocation = resp;
                        locationCtrl.text = resp?.address ?? "";

                        // print(selectedLocation?.latlng.lat);
                        // print(locationCtrl.text);
                        setState(() {});
                      },
                      label: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            "Set pin location",
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (selectedLocation != null)
                            const Padding(
                              padding: EdgeInsets.only(left: 6.0),
                              child: Icon(Icons.check, color: Colors.green),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () async {
                          if (selectedLocation == null) {
                            showAppSnackBar("Please, select delivery location");
                            return;
                          }
                          setState(() => loading = true);
                          widget.addressModel == null
                              ? await FBFireStore.users
                                  .doc(FBAuth.auth.currentUser?.uid)
                                  .update({
                                    "addresses.${getRandomId(6)}": {
                                      'name': nameCtrl.text,
                                      'flat': flatCtrl.text,
                                      'area': areaCtrl.text,
                                      'landmark': landmarkCtrl.text,
                                      'city': "Vadodara",
                                      'state': "Gujarat",
                                      'addressType':
                                          addressType[selAddressTypeIndex],
                                      'lat': selectedLocation?.latlng.lat,
                                      'long': selectedLocation?.latlng.lng,
                                      'location': locationCtrl.text,
                                    },
                                  })
                              : await FBFireStore.users
                                  .doc(FBAuth.auth.currentUser?.uid)
                                  .update({
                                    "addresses.${widget.addressModel!.id}": {
                                      'name': nameCtrl.text,
                                      'flat': flatCtrl.text,
                                      'area': areaCtrl.text,
                                      'landmark': landmarkCtrl.text,
                                      'city': "Vadodara",
                                      'state': "Gujarat",
                                      'addressType':
                                          addressType[selAddressTypeIndex],
                                      'lat': selectedLocation?.latlng.lat,
                                      'long': selectedLocation?.latlng.lng,
                                    },
                                  });
                          setState(() => loading = false);
                          if (context.mounted) context.pop();
                        },
                        style: ElevatedButton.styleFrom(
                          minimumSize: const Size.fromHeight(52),
                          backgroundColor: themeColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Container(
                            constraints: const BoxConstraints(minWidth: 100),
                            child: Center(
                              child:
                                  loading
                                      ? const CircularProgressIndicator(
                                        color: Colors.white,
                                      )
                                      : const Text(
                                        "Save Address",
                                        style: TextStyle(
                                          letterSpacing: 1.5,
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    if (widget.addressModel == null) return;
    try {
      nameCtrl.text = widget.addressModel!.name;
      flatCtrl.text = widget.addressModel!.flat;
      areaCtrl.text = widget.addressModel!.area;
      landmarkCtrl.text = widget.addressModel?.landmark ?? "";
      selAddressTypeIndex = addressType.indexOf(
        widget.addressModel?.addressType ?? "Home",
      );
      selectedLocation = SelectedLocation(
        latlng: Location(
          lat: widget.addressModel?.lat.toDouble() ?? 0,
          lng: widget.addressModel?.long.toDouble() ?? 0,
        ),
        address: widget.addressModel?.location ?? "",
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  InputDecoration _textFeildDecoration() {
    return InputDecoration(
      isDense: true,
      floatingLabelAlignment: FloatingLabelAlignment.start,
      enabledBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: Color(0xffd8d8d8), width: .8),
        borderRadius: BorderRadius.circular(6),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: Color(0xffd8d8d8), width: .8),
        borderRadius: BorderRadius.circular(6),
      ),
      border: OutlineInputBorder(
        borderSide: const BorderSide(color: Color(0xffd8d8d8), width: .8),
        borderRadius: BorderRadius.circular(6),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: const BorderSide(color: Color(0xfff5f6fb), width: .8),
        borderRadius: BorderRadius.circular(6),
      ),
      floatingLabelStyle: const TextStyle(
        fontSize: 14,
        color: Color(0xff787c88),
        fontWeight: FontWeight.w600,
      ),
      labelStyle: const TextStyle(
        fontSize: 14,
        color: Color(0xff787c88),
        fontWeight: FontWeight.w700,
      ),
    );
  }
}
