// ignore_for_file: no_wildcard_variable_uses

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/shared/methods.dart';
import 'package:swagat_user/shared/router.dart';
import 'package:swagat_user/views/account/widgets/your_orders.dart';
import '../../../models/order_models/order_model.dart';
import '../../../shared/theme.dart';

class CurrentOrder extends StatelessWidget {
  const CurrentOrder({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        return Scaffold(
          // backgroundColor: const Color(0xfff5f6fb),
          backgroundColor: Colors.white,
          appBar: AppBar(
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 1,
            titleSpacing: 4,
            scrolledUnderElevation: 5,
            shadowColor: Colors.black45,
            centerTitle: false,
            title: const Text('Current Orders'),
          ),
          body:
              ctrl.currentOrders.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text(
                          "No current orders to display",
                          style: TextStyle(fontSize: 18),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: ElevatedButton(
                            style: ButtonStyle(
                              fixedSize: const WidgetStatePropertyAll(
                                Size.fromHeight(10),
                              ),
                              padding: const WidgetStatePropertyAll(
                                EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 10,
                                ),
                              ),
                              elevation: const WidgetStatePropertyAll(0),
                              backgroundColor: const WidgetStatePropertyAll(
                                themeColor,
                              ),
                              foregroundColor: const WidgetStatePropertyAll(
                                Colors.white,
                              ),
                              shape: WidgetStatePropertyAll(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                            onPressed: () {
                              context.pop();
                              Get.find<HomeCtrl>().selectedIndex = 1;
                              context.go(Routes.wrapper);
                            },
                            child: const Text(
                              "Go to Menu",
                              style: TextStyle(fontSize: 18),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                  : SingleChildScrollView(
                    padding: const EdgeInsets.all(15),
                    child: Column(
                      children: [
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: ctrl.currentOrders.length,
                          itemBuilder: (context, index) {
                            return OrderCard(
                              orderModel: ctrl.currentOrders[index],
                              fromCurrentOrder: true,
                            );
                            // return _OrderTile(order: ctrl.currentOrders[index]);
                          },
                          separatorBuilder: (context, index) {
                            return const SizedBox(height: 12);
                          },
                        ),
                      ],
                    ),
                  ),
          // body: ,
        );
      },
    );
  }
}

class _OrderTile extends StatelessWidget {
  const _OrderTile({required this.order});
  final OrderModel order;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.push('${Routes.liveorder}/${order.docId}');
      },
      child: Card(
        elevation: 2,
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Colors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        order.orderId,
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(order.type, style: const TextStyle(fontSize: 15)),
                    ],
                  ),
                  Text(
                    order.time?.toDate().goodTime() ?? "-",
                    style: const TextStyle(fontSize: 15),
                  ),
                ],
              ),
              const Divider(color: Colors.grey, thickness: .7),
              ListView.builder(
                itemCount: order.foodItems.length,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  final foodItem = order.foodItems[index];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text("${index + 1})"),
                          const SizedBox(width: 8),
                          Text(
                            "${foodItem.name} - ${foodItem.variantName.capitalizeFirst}",
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(fontSize: 15),
                          ),
                          const SizedBox(width: 5),
                          const Spacer(),
                          const Text("x", style: TextStyle(fontSize: 15)),
                          const SizedBox(width: 5),
                          Text(
                            foodItem.qty.toString(),
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: foodItem.addOns.length,
                        shrinkWrap: true,
                        itemBuilder: (context, idx) {
                          final adn = foodItem.addOns[idx];
                          return Padding(
                            padding: const EdgeInsets.only(left: 12.5),
                            child: Text(
                              "- ${adn.name}",
                              style: const TextStyle(fontSize: 15),
                            ),
                          );
                        },
                      ),
                      const Divider(color: Colors.grey, thickness: .3),
                    ],
                  );
                },
              ),
              if (order.orderNote.isNotEmpty) Text("Note: ${order.orderNote}"),
              const Divider(color: Colors.grey, thickness: .7),
              order.rider == null
                  ? const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [Text("Rider Name"), Text("Rider Number")],
                  )
                  : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(order.rider!.name),
                      Text(order.rider!.number),
                    ],
                  ),
            ],
          ),
        ),
      ),
    );
  }

  num get totalQty => order.foodItems
      .map((e) => e.qty)
      .reduce((value, element) => value + element);
}
