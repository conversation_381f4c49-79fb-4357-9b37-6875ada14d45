import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:swagat_user/shared/firebase.dart';
import 'package:swagat_user/shared/methods.dart';
import 'package:swagat_user/shared/theme.dart';

import '../../../controllers/home_controller.dart';
import '../../../models/order_models/order_model.dart';
import '../../../shared/router.dart';

class MyWidget extends StatefulWidget {
  const MyWidget({super.key});

  @override
  State<MyWidget> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  @override
  Widget build(BuildContext context) {
    return const Placeholder();
  }
}

class YourOrdersPage extends StatefulWidget {
  const YourOrdersPage({super.key});

  @override
  State<YourOrdersPage> createState() => _YourOrdersPage();
}

class _YourOrdersPage extends State<YourOrdersPage> {
  final _orders = [];
  var lastVisible;
  bool fetchingInitialData = false;
  bool fetchingOtherData = false;

  @override
  void initState() {
    super.initState();
    _getInitialOrders();
  }

  Future<void> _getInitialOrders() async {
    try {
      setState(() {
        fetchingInitialData = true;
      });
      QuerySnapshot snapshot =
          await FBFireStore.orders
              .where('userData.docId', isEqualTo: FBAuth.auth.currentUser!.uid)
              .where('isPaid', isEqualTo: true)
              .orderBy('time', descending: true)
              .limit(5)
              .get();
      if (snapshot.size > 0) {
        _orders.addAll(snapshot.docs.map((doc) => _parseOrder(doc)));
        lastVisible = snapshot.docs.last;
      }
      setState(() {
        fetchingInitialData = false;
      });
    } catch (e) {
      debugPrint(e.toString());
      setState(() {
        fetchingInitialData = false;
      });
    }
  }

  Future<void> _loadMoreOrders() async {
    // Get the next 5 orders after the last visible one
    try {
      setState(() {
        fetchingOtherData = true;
      });
      QuerySnapshot snapshot =
          await FBFireStore.orders
              .where('userData.docId', isEqualTo: FBAuth.auth.currentUser!.uid)
              .where('isPaid', isEqualTo: true)
              .orderBy('time', descending: true)
              .startAfterDocument(lastVisible)
              .limit(5)
              .get();

      if (snapshot.docs.isNotEmpty) {
        _orders.addAll(snapshot.docs.map((doc) => _parseOrder(doc)));
        lastVisible = snapshot.docs.last;
      }
      setState(() {
        fetchingOtherData = false;
      });
    } catch (e) {
      debugPrint(e.toString());
      setState(() {
        fetchingOtherData = false;
      });
    }
  }

  OrderModel _parseOrder(QueryDocumentSnapshot doc) {
    return OrderModel.fromSnap(doc);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xfff5f6fb),
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        elevation: 1,
        titleSpacing: 4,
        scrolledUnderElevation: 5,
        shadowColor: Colors.black45,
        centerTitle: false,
        title: const Text('Your Orders'),
      ),
      body:
          fetchingInitialData
              ? Center(
                child: CircularProgressIndicator(
                  color: themeColor,
                  strokeWidth: 3.5,
                ),
              )
              : _orders.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      "No orders to display",
                      style: TextStyle(fontSize: 18),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 10.0),
                      child: ElevatedButton(
                        style: ButtonStyle(
                          fixedSize: const WidgetStatePropertyAll(
                            Size.fromHeight(10),
                          ),
                          padding: const WidgetStatePropertyAll(
                            EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                          ),
                          elevation: const WidgetStatePropertyAll(0),
                          backgroundColor: const WidgetStatePropertyAll(
                            themeColor,
                          ),
                          foregroundColor: const WidgetStatePropertyAll(
                            Colors.white,
                          ),
                          shape: WidgetStatePropertyAll(
                            RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                        onPressed: () {
                          context.pop();
                          Get.find<HomeCtrl>().selectedIndex = 1;
                          context.go(Routes.wrapper);
                        },
                        child: const Text(
                          "Go to Menu",
                          style: TextStyle(fontSize: 18),
                        ),
                      ),
                    ),
                  ],
                ),
              )
              : ListView.separated(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 15,
                ),
                itemCount:
                    _orders.length + 1, // Add 1 for the "Load More" button
                itemBuilder: (context, index) {
                  if (index == _orders.length) {
                    // Show "Load More" button at the end
                    return fetchingOtherData
                        ? Center(
                          child: CircularProgressIndicator(
                            color: themeColor,
                            strokeWidth: 3.5,
                          ),
                        )
                        : Center(
                          child: ElevatedButton(
                            onPressed: _loadMoreOrders,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: themeColor,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text(
                              'Load More',
                              style: TextStyle(fontWeight: FontWeight.w700),
                            ),
                          ),
                        );
                  }

                  final order = _orders[index];
                  return OrderCard(orderModel: order, fromCurrentOrder: false);
                },
                separatorBuilder: (context, index) {
                  return const SizedBox(height: 20);
                },
              ),
    );
  }
}

class OrderCard extends StatelessWidget {
  const OrderCard({
    super.key,
    required this.orderModel,
    required this.fromCurrentOrder,
  });
  final OrderModel orderModel;
  final bool fromCurrentOrder;

  @override
  Widget build(BuildContext context) {
    final itemList = orderModel.foodItems;
    return InkWell(
      onTap: () {
        fromCurrentOrder
            ? context.push('${Routes.liveorder}/${orderModel.docId}')
            : context.push(
              '${Routes.orders}/${orderModel.docId}',
              extra: orderModel,
            );
      },
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              blurRadius: 2,
              color: Colors.grey.shade400,
              offset: const Offset(0, .7),
            ),
          ],
          borderRadius: BorderRadius.circular(5),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: const BoxDecoration(
                // borderRadius: BorderRadius.only(
                //   topLeft: Radius.circular(5),
                //   topRight: Radius.circular(5),
                // ),
                color: Color.fromARGB(255, 236, 238, 248),
                // color: Colors.grey.shade200,
                // color: Color(0xfff5f6fb),
                // border: Border.all(color: Colors.grey.shade300),
              ),
              width: double.maxFinite,
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    orderModel.orderId,
                    style: const TextStyle(
                      letterSpacing: .7,
                      // color: Colors.black,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Icon(
                    CupertinoIcons.chevron_forward,
                    // color: Colors.black,
                    size: 20,
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
              width: double.maxFinite,
              decoration: const BoxDecoration(
                color: Colors.white,
                // borderRadius: BorderRadius.only(
                //     bottomRight: Radius.circular(5),
                //     bottomLeft: Radius.circular(5)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 5,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        itemList[index].qty.toString(),
                                        style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                      const SizedBox(width: 5),
                                      const Text('x'),
                                      const SizedBox(width: 5),
                                      Text(
                                        itemList[index].name,
                                        style: const TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        itemList[index].qty.toString(),
                                        style: const TextStyle(
                                          fontWeight: FontWeight.w600,
                                          color: Colors.transparent,
                                        ),
                                      ),
                                      const SizedBox(width: 5),
                                      const Text(
                                        'x',
                                        style: TextStyle(
                                          color: Colors.transparent,
                                        ),
                                      ),
                                      const SizedBox(width: 5),
                                      Text(
                                        itemList[index].variantName,
                                        style: const TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            );
                          },
                          separatorBuilder: (context, index) {
                            return const Divider(
                              thickness: .13,
                              color: Colors.grey,
                            );
                          },
                          itemCount: itemList.length,
                        ),
                      ),
                    ],
                  ),
                  /* 
                  ...List.generate(
                    itemList.length,
                    (index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  itemList[index].qty.toString(),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                const SizedBox(width: 5),
                                const Text('x'),
                                const SizedBox(width: 5),
                                Text(
                                  itemList[index].name,
                                  style: const TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.w600),
                                ),
                              ],
                            ),
                            Row(
                              children: [
                                Text(
                                  itemList[index].qty.toString(),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w600,
                                    color: Colors.transparent,
                                  ),
                                ),
                                const SizedBox(width: 5),
                                const Text(
                                  'x',
                                  style: TextStyle(
                                    color: Colors.transparent,
                                  ),
                                ),
                                const SizedBox(width: 5),
                                Text(
                                  itemList[index].variantName,
                                  style: const TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.w500),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                 */
                  Divider(thickness: .2, color: Colors.grey.shade400),
                  Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text:
                              'Order placed on ${orderModel.time?.toDate().goodDayDate2()}, ${orderModel.time?.toDate().goodTime()}',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color:
                                true
                                    ? null
                                    : Color.fromARGB(255, 123, 123, 125),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        orderModel.status,
                        style: const TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          color:
                              true ? null : Color.fromARGB(255, 123, 123, 125),
                        ),
                      ),
                      Text(
                        '₹${orderModel.totalAmountPaid.toStringAsFixed(2)}',
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
