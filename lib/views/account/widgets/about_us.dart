import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AboutUs extends StatelessWidget {
  const AboutUs({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        elevation: 1,
        titleSpacing: 4,
        scrolledUnderElevation: 5,
        shadowColor: Colors.black45,
        centerTitle: false,
        title: const Text("About Us"),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: SizedBox(
                height: 120,
                width: 250,
                child: Image.asset('assets/swagat-logo-main.png'),
              ),
            ),
            const SizedBox(height: 15),
            Center(
              child: Text(
                'Since 2003: From The First Bite To The Last!',
                textAlign: TextAlign.center,
                style: GoogleFonts.aBeeZee(
                  fontWeight: FontWeight.bold,
                  fontSize: 21,
                ),
              ),
            ),
            const SizedBox(height: 15),
            const Text(
              'We invite you to embark on a culinary journey where every flavour is a chapter, and every meal leaves you with a memorable and delicious story to savour.',
              style: TextStyle(
                fontSize: 15,
                height: 1.6,
                letterSpacing: 0.5,
                wordSpacing: 1,
              ),
            ),
            const SizedBox(height: 15),
            const Text(
              "From carefully crafted dishes made with the finest ingredients to our inviting ambiance, we invite you to indulge in a culinary journey that celebrates both tradition and innovation.",
              style: TextStyle(
                fontSize: 15,
                height: 1.6,
                letterSpacing: 0.5,
                wordSpacing: 1,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
