import 'package:flutter/material.dart';

class TermsCondition extends StatelessWidget {
  const TermsCondition({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        elevation: 1,
        titleSpacing: 4,
        scrolledUnderElevation: 5,
        shadowColor: Colors.black45,
        centerTitle: false,
        title: const Text("Terms & Condiitons"),
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 10),
            Text(
              "1. Condition",
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
            ),
            SizedBox(height: 7),
            Text(
                "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed sed dictum ipsum. Sed ut velit quis massa convallis aliquam. Nullam et elit quis nulla aliquet malesuada eget vitae lacus. Mauris sed tellus lacus. Vestibulum eu ante ac libero auctor porttitor et sit amet metus. Phasellus congue accumsan porttitor. Nunc non mauris quam. Nulla mollis tempor posuere. Duis non nulla iaculis, elementum augue eget, ornare nisl.",
                style: TextStyle(fontSize: 11)),
            SizedBox(height: 15),
            Text(
              "2. Condition",
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
            ),
            SizedBox(height: 7),
            Text(
                "Cras id sapien convallis, ornare risus in, facilisis eros. Mauris tristique tempor dui nec blandit. Nunc dictum elit nec aliquam euismod. Duis aliquet mollis dui, a vestibulum est congue eu. Nulla in est felis. Morbi nec tincidunt libero. Suspendisse id rutrum dui. Nunc iaculis lectus id nisi facilisis dictum. Donec ut mauris at nibh rhoncus dapibus. Mauris est est, lacinia nec arcu ac, semper lacinia diam.",
                style: TextStyle(fontSize: 11)),
            SizedBox(height: 15),
            Text(
              "3. Condition",
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
            ),
            SizedBox(height: 7),
            Text(
                "Ut nibh elit, facilisis non volutpat id, euismod porttitor mi. Vivamus at lectus eu odio auctor ultricies. Sed molestie ligula ut turpis porttitor, non lacinia nulla dictum. Duis fringilla dui nec mi accumsan, vel accumsan lectus molestie. Donec augue neque, congue ut lacus ut, efficitur porttitor odio. Nunc eget scelerisque mauris. Nam ultrices iaculis justo, sit amet efficitur eros tincidunt at. Morbi placerat id ex et congue. Quisque leo mi, cursus nec bibendum vitae, efficitur vitae quam. Maecenas eget arcu posuere, interdum elit eget, facilisis arcu. Mauris id pellentesque nibh. Sed eget lorem facilisis, lacinia ipsum ut, mattis dolor. Maecenas sodales vel eros non efficitur. Proin sit amet malesuada lectus.",
                style: TextStyle(fontSize: 11)),
            SizedBox(height: 20),
            Text(
              "4. Condition",
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
            ),
            SizedBox(height: 7),
            Text(
                "Morbi ultrices dolor sit amet lobortis egestas. Sed nec pharetra mi. Vivamus posuere nunc vel tellus tristique aliquet. Vestibulum eros felis, varius feugiat massa at, mattis feugiat mi. In quis velit quis lacus consequat venenatis. Fusce convallis ipsum ut feugiat congue. Donec bibendum dictum posuere. Mauris pretium ipsum justo, non aliquam nisl mattis sed. Donec maximus nisl eu neque placerat tempor. Aliquam vitae arcu sem.",
                style: TextStyle(fontSize: 11)),
          ],
        ),
      ),
    );
  }
}
