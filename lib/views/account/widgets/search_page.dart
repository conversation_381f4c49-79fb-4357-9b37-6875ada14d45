// ignore_for_file: no_wildcard_variable_uses

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import '../../homepage/homepage_widegts/special_item_widget.dart';

class SearchPage extends StatelessWidget {
  const SearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    final searchController = TextEditingController();
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        surfaceTintColor: Colors.white,
        elevation: 1,
        titleSpacing: 4,
        scrolledUnderElevation: 5,
        shadowColor: Colors.black45,
        centerTitle: false,
        title: const Text("Search page", style: TextStyle(fontSize: 20)),
      ),
      body: GetBuilder<HomeCtrl>(
        builder: (ctrl) {
          final filtered =
              ctrl.foods
                  .where(
                    (element) => element.name.toLowerCase().contains(
                      searchController.text.toLowerCase(),
                    ),
                  )
                  .toList();
          return CustomScrollView(
            slivers: <Widget>[
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(14.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          autofocus: true,
                          controller: searchController,
                          onChanged: (value) => ctrl.update(),
                          decoration: InputDecoration(
                            contentPadding: const EdgeInsets.symmetric(
                              vertical: 12,
                            ),
                            hintText: "What's on Your Mind",
                            hintStyle: const TextStyle(color: Colors.black54),
                            enabledBorder: const OutlineInputBorder(
                              borderSide: BorderSide(
                                width: 1,
                                color: Colors.grey,
                                style: BorderStyle.solid,
                              ),
                              borderRadius: BorderRadius.all(
                                Radius.circular(12),
                              ),
                            ),
                            focusedBorder: const OutlineInputBorder(
                              borderSide: BorderSide(
                                width: 1,
                                color: Colors.grey,
                                style: BorderStyle.solid,
                              ),
                              borderRadius: BorderRadius.all(
                                Radius.circular(12),
                              ),
                            ),
                            prefixIcon: const Icon(
                              CupertinoIcons.search,
                              size: 20,
                              color: Colors.grey,
                            ),
                            suffixIcon:
                                searchController.text.isEmpty
                                    ? const SizedBox()
                                    : InkWell(
                                      onTap: () {
                                        searchController.clear();
                                        ctrl.update();
                                      },
                                      child: const Icon(
                                        CupertinoIcons.xmark_circle_fill,
                                        color: Colors.grey,
                                        size: 28,
                                      ),
                                    ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child:
                    searchController.text.isEmpty
                        ? const SizedBox()
                        : Column(
                          children: [
                            ListView.builder(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              itemCount: filtered.length,
                              padding: const EdgeInsets.only(top: 8.0),
                              itemBuilder:
                                  (context, index) =>
                                      SpecialItemCard(food: filtered[index]),
                            ),
                          ],
                        ),
              ),
            ],
          );
        },
      ),
    );
  }
}
