// ignore_for_file: no_wildcard_variable_uses

import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:swagat_user/controllers/auth_controller.dart';
import 'package:swagat_user/shared/theme.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  bool loading = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GetBuilder<AuthCtrl>(
        builder: (ctrl) {
          return Stack(
            fit: StackFit.expand,
            children: [
              Container(
                color: Colors.white,
                child: Image.asset('assets/33.jpg', fit: BoxFit.cover),
              ),
              BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                child: Column(
                  children: [
                    Expanded(
                      flex: 4,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 30.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 20),
                            Expanded(
                              child: Image.asset(
                                'assets/swagat1.png',
                                fit: BoxFit.cover,
                                height: 140,
                              ),
                            ),
                            const Text(
                              "Swagat\nCorner",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 36,
                              ),
                            ),
                            Expanded(
                              child: Image.asset(
                                'assets/swagat2.png',
                                fit: BoxFit.cover,
                                height: 130,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 4,
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        // color: Colors.white,
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            const Spacer(flex: 3),
                            const SizedBox(
                              child: Text(
                                "Welcome",
                                style: TextStyle(
                                  color: themeColor,
                                  fontSize: 30,
                                ),
                              ),
                            ),
                            const SizedBox(
                              child: Text(
                                "Sign in with phone number",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            const Spacer(flex: 1),
                            PhoneFieldHint(
                              controller: ctrl.phoneCtrl,
                              decoration: _decor(),
                            ),
                            Row(
                              children: [
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 20.0),
                                    child: ElevatedButton(
                                      onPressed: () async {
                                        setState(() => loading = true);
                                        await ctrl.sentOtp(context);
                                        setState(() => loading = false);
                                      },
                                      style: ElevatedButton.styleFrom(
                                        fixedSize: const Size.fromHeight(55),
                                        elevation: 0,
                                        backgroundColor: themeColor,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                      ),
                                      child:
                                          ctrl.sendingOtp
                                              ? const CircularProgressIndicator(
                                                color: Colors.white,
                                              )
                                              : const Padding(
                                                padding: EdgeInsets.symmetric(
                                                  vertical: 16.0,
                                                ),
                                                child: Text(
                                                  "Sign In",
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 18,
                                                  ),
                                                ),
                                              ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const Spacer(flex: 4),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  InputDecoration _decor() {
    return InputDecoration(
      prefixIcon: const Icon(CupertinoIcons.phone),
      fillColor: Colors.grey.shade50,
      filled: true,
      hintText: "Phone Number",
      alignLabelWithHint: true,
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.white, width: 1.5),
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide.none,
      ),
    );
  }
}

      /* 
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Sign in With Phone'),
        centerTitle: true,
      ),
      body: GetBuilder<AuthCtrl>(
        builder: (ctrl) {
          return Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                TextField(
                  controller: ctrl.phoneCtrl,
                  keyboardType: TextInputType.phone,
                  decoration: const InputDecoration(
                    hintText: 'Enter phone Number',
                  ),
                ),
                const SizedBox(height: 10),
                ElevatedButton(
                    onPressed: () => ctrl.sentOtp(context),
                    child: const Text('Sign In'))
              ],
            ),
          );
        },
      ),
       */