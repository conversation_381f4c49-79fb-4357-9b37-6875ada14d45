import 'package:flutter/material.dart';

class Customshape extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    double height = size.height;
    double width = size.width;

    var path = Path();
/*     path.lineTo(0, height * .25);
    path.quadraticBezierTo(width * .01, height * .18, width * .15, height * .2);
    path.lineTo(width * .85, height * .25);
    path.quadraticBezierTo(width, height * .25, width, height * .35);
    path.lineTo(width, height);
    path.lineTo(0, height);
    path.close(); */

    // Working
    path.lineTo(0, height * .25);
    path.cubicTo(width * .34, height * .09, width * .47, height * .48,
        width * .8, height * .32);
    path.quadraticBezierTo(width * .91, height * .28, width, height * .32);
    path.lineTo(width, height);
    path.lineTo(0, height);
    path.close();

/*     path.lineTo(0, height * .2);
    path.cubicTo(width * .3, height * .01, width * .47, height * .48,
        width * .8, height * .3);
    path.quadraticBezierTo(width * .91, height * .24, width, height * .32);
    path.lineTo(width, height);
    path.lineTo(0, height);
    path.close(); */
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true;
  }
}

class Customshape2 extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    double height = size.height;
    double width = size.width;

    var path = Path();
    path.lineTo(0, height * .25);
    path.cubicTo(width * .34, height * .09, width * .47, height * .48,
        width * .8, height * .32);
    path.quadraticBezierTo(width * .91, height * .28, width, height * .32);
    path.lineTo(width, height);
    path.lineTo(0, height);
    path.close();

    // path.lineTo(0, height * .22);
    // path.quadraticBezierTo(width * .1, height * .2, width * .12, height * .3);
    /*    path.lineTo(0, height * .3);
    path.cubicTo(
        width * .6, height * .1, width * .47, height * .48, width, height * .3);
    path.lineTo(width, height);
    path.lineTo(0, height);
    path.close(); */
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) {
    return true;
  }
}
