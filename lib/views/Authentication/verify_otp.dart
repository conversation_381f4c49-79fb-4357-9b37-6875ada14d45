// ignore_for_file: no_wildcard_variable_uses

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:swagat_user/controllers/auth_controller.dart';

class VerifyOtp extends StatefulWidget {
  const VerifyOtp({super.key});

  @override
  State<VerifyOtp> createState() => _VerifyOtpState();
}

class _VerifyOtpState extends State<VerifyOtp> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(title: const Text('Verify OTP'), centerTitle: true),
      body: GetBuilder<AuthCtrl>(
        builder: (ctrl) {
          return Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                PinFieldAutoFill(
                  controller: ctrl.otpCtrl,
                  autoFocus: true,
                  decoration: BoxLooseDecoration(
                    strokeColorBuilder: FixedColorBuilder(Colors.grey.shade700),
                  ),
                  onCodeSubmitted: (p0) {
                    if (ctrl.otpCtrl.text.length > 5) {
                      ctrl.createCredsAndSignIn(context);
                    }
                  },
                  onCodeChanged: (p0) {
                    if (ctrl.otpCtrl.text.length > 5) {
                      ctrl.createCredsAndSignIn(context);
                    }
                  },
                ),

                /* 
                TextField(
                  maxLength: 6,
                  controller: ctrl.otpCtrl,
                  keyboardType: TextInputType.phone,
                  decoration: const InputDecoration(
                    hintText: 'Enter OTP',
                  ),
                ),
                 */
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          fixedSize: const Size.fromHeight(55),
                          elevation: 0,
                          backgroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: () => ctrl.createCredsAndSignIn(context),
                        child:
                            ctrl.verifyingOtp
                                ? const CircularProgressIndicator(
                                  color: Colors.white,
                                )
                                : const Text(
                                  'Verify',
                                  style: TextStyle(
                                    letterSpacing: 1,
                                    fontSize: 18,
                                    color: Colors.white,
                                  ),
                                ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                TextButton(
                  onPressed: () {
                    ctrl.sentOtp(context);
                  },
                  child: const Text(
                    "Resend",
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
