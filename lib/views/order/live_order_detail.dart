// ignore_for_file: no_wildcard_variable_uses

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/models/outlet_model.dart';
import 'package:swagat_user/shared/const.dart';
import 'package:swagat_user/shared/methods.dart';
import 'package:swagat_user/views/order/live_tracking.dart';
import '../../models/order_models/food_in_order_model.dart';
import '../../models/order_models/order_model.dart';
import '../../models/order_models/rider_in_order_model.dart';
import '../../shared/firebase.dart';

class LiveOrderDetailsPage extends StatefulWidget {
  final String orderId;

  const LiveOrderDetailsPage({super.key, required this.orderId});

  @override
  State<LiveOrderDetailsPage> createState() => _LiveOrderDetailsPageState();
}

class _LiveOrderDetailsPageState extends State<LiveOrderDetailsPage> {
  OutletModel? outlet;
  @override
  void initState() {
    super.initState();
  }

  getOrderedOutlet(String? outletId) async {
    outlet = await FBFireStore.outlets
        .doc(outletId)
        .get()
        .then((value) => OutletModel.fromDocSnap(value));
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final theme = ThemeData(
      textTheme: GoogleFonts.montserratAlternatesTextTheme(),
    );
    // final theme = Theme.of(context);
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final order = ctrl.currentOrders.firstWhereOrNull(
          (element) => element.docId == widget.orderId,
        );
        getOrderedOutlet(order?.outletDocId);
        return Scaffold(
          backgroundColor: Colors.grey.shade100,
          appBar: AppBar(
            // actions: [
            //   Padding(
            //     padding: const EdgeInsets.only(right: 10.0),
            //     child: ElevatedButton(
            //       style: ElevatedButton.styleFrom(
            //         backgroundColor: Colors.redAccent,
            //         foregroundColor: Colors.white,
            //       ),
            //       onPressed: () {},
            //       child: const Text(
            //         'Cancel',
            //         style: TextStyle(fontWeight: FontWeight.bold),
            //       ),
            //     ),
            //   ),
            // ],
            centerTitle: false,
            titleSpacing: 4,
            title: InkWell(
              onTap: () async {
                if (order != null && order.rider != null) {
                  await FBFireStore.rider.doc(order.rider?.docId).update({
                    "lat": 22.327697177402996,
                    "lng": 73.21116911377727,
                  });
                }
              },
              child: const Text('Order Details'),
            ),
            surfaceTintColor: Colors.white,
          ),
          body:
              order == null
                  ? const Center(child: Text("Order was completed!"))
                  : Stack(
                    children: [
                      // order.rider != null
                      true
                          ? OrderLiveTraking(order: order)
                          : const Center(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SizedBox(height: 20),
                                Text("Rider will be assigned soon"),
                              ],
                            ),
                          ),

                      DraggableScrollableSheet(
                        maxChildSize: .9,
                        minChildSize: .2,
                        initialChildSize: .4,

                        builder: (context, scrollController) {
                          return Container(
                            decoration: const BoxDecoration(
                              color: Color.fromARGB(255, 243, 244, 250),

                              // color: Color(0xfff5f6fb),
                              // color: Color.fromARGB(255, 248, 248, 248),
                              borderRadius: BorderRadius.vertical(
                                top: Radius.circular(24),
                              ),
                            ),
                            padding: const EdgeInsets.only(
                              top: 12.0,
                              // horizontal: 15.0,
                            ),
                            child: Column(
                              children: [
                                Center(
                                  child: Container(
                                    height: 5,
                                    width: 50,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(45),
                                      color: Colors.grey,
                                    ),
                                  ),
                                ),
                                SizedBox(height: 10),
                                Expanded(
                                  child: SingleChildScrollView(
                                    physics: const ClampingScrollPhysics(),
                                    controller: scrollController,
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12.0,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const SizedBox(height: 15),
                                          // Order Summary
                                          _buildOrderSummary(order, theme),
                                          const SizedBox(height: 15.0),

                                          // Rider Details (if available)
                                          // if (order.rider != null)
                                          if (order.type ==
                                              OrderType.delivery) ...[
                                            _buildRiderDetails(
                                              order.rider,
                                              theme,
                                            ),

                                            const SizedBox(height: 15),
                                          ],
                                          // Food Items
                                          _buildFoodItemsList(
                                            order.foodItems,
                                            theme,
                                          ),

                                          const SizedBox(height: 15),
                                          // Order Note (if any)

                                          // Total Price & Payment Details
                                          _buildTotalPriceAndPaymentDetails(
                                            order,
                                            theme,
                                          ),
                                          if (order.orderNote.isNotEmpty) ...[
                                            const SizedBox(height: 15),

                                            _buildOrderNote(
                                              order.orderNote,
                                              theme,
                                            ),
                                          ],
                                          const SizedBox(height: 10),
                                          OutlinedButton(
                                            style: OutlinedButton.styleFrom(
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                              ),

                                              side: BorderSide(
                                                width: .5,
                                                color: Color.fromARGB(
                                                  255,
                                                  152,
                                                  152,
                                                  152,
                                                ),
                                              ),
                                              foregroundColor: Color(
                                                0xff4F4F4F,
                                              ),
                                            ),
                                            onPressed: () async {
                                              showDialog(
                                                context: context,
                                                builder: (context) {
                                                  return AlertDialog(
                                                    title: Text("Cancel Order"),
                                                    content: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Text(
                                                          "Are you sure you want to cancel this order?",
                                                          style: TextStyle(
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            fontSize: 14,
                                                          ),
                                                        ),
                                                        SizedBox(height: 5),
                                                        Text(
                                                          "* Refunds are not available on cancellations.",
                                                          style: TextStyle(
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            fontSize: 10,
                                                          ),
                                                        ),
                                                      ],
                                                    ),

                                                    actions: [
                                                      TextButton(
                                                        onPressed: () {
                                                          Navigator.pop(
                                                            context,
                                                          );
                                                        },
                                                        child: Text("Go Back"),
                                                      ),
                                                      TextButton(
                                                        onPressed: () async {
                                                          await FBFireStore
                                                              .orders
                                                              .doc(order.docId)
                                                              .update({
                                                                "status":
                                                                    "Cancelled",
                                                                "cancelledBy":
                                                                    "User",
                                                                "cancelledAt":
                                                                    FieldValue.serverTimestamp(),
                                                              });
                                                          Navigator.pop(
                                                            context,
                                                          );
                                                        },
                                                        child: Text(
                                                          "Yes, Cancel",
                                                        ),
                                                      ),
                                                    ],
                                                  );
                                                },
                                              );
                                            },
                                            child: Text("Cancel Order"),
                                          ),
                                          SizedBox(height: 15),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ],
                  ),
        );
      },
    );
  }

  Widget _buildOrderSummary(OrderModel order, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Colors.white,
        // color: Color.fromARGB(255, 250, 250, 250),
        // boxShadow: [
        //   BoxShadow(
        //     color: const Color.fromARGB(19, 0, 0, 0),
        //     blurRadius: 10,
        //     spreadRadius: .8,
        //     offset: Offset(.2, 1.5),
        //   ),
        // ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (outlet != null)
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.all(8.0),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: SizedBox(
                    height: 35,
                    width: 35,
                    child: Image.asset('assets/swagat1.png'),
                  ),
                ),
                const SizedBox(width: 13),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Swagat Corner',
                      style: TextStyle(
                        color: Color(0xff4F4F4F),
                        fontWeight: FontWeight.w600,
                        fontSize: 15,
                      ),
                    ),
                    Text(
                      outlet?.outletName ?? '',
                      style: const TextStyle(
                        color: Color(0xff4F4F4F),
                        fontWeight: FontWeight.w600,
                        fontSize: 17,
                      ),
                    ),
                  ],
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 5),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    color: Colors.white,
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: 'OTP: ',
                          style: TextStyle(
                            color: Color.fromARGB(255, 91, 91, 91),
                            fontWeight: FontWeight.w500,
                            fontSize: 13.5,
                          ),
                        ),
                        TextSpan(
                          text: order.otp.toString(),
                          style: const TextStyle(color: Color(0xff4F4F4F)),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          const SizedBox(height: 8),
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text.rich(
                    TextSpan(
                      children: [
                        const TextSpan(
                          text: 'Order Id: ',
                          style: TextStyle(
                            color: Color.fromARGB(255, 91, 91, 91),
                            fontWeight: FontWeight.w500,
                            fontSize: 17,
                          ),
                        ),
                        TextSpan(
                          text: order.orderId,
                          style: const TextStyle(
                            color: Color(0xff4F4F4F),
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    '${order.time!.toDate().goodDayMonth()}, ${order.time!.toDate().goodTime()}',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey.shade500,
                      // color: Color(0xff4F4F4F),
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    order.status == "Recieved" ? "Order Placed" : order.status,
                    style: const TextStyle(fontSize: 17),
                  ),
                  IntrinsicHeight(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '$rsSymbol${order.totalAmountPaid.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey.shade500,
                            // color: Color(0xff4F4F4F),
                          ),
                        ),
                        const VerticalDivider(),
                        Text(
                          order.isPaid ? "Paid" : "Unpaid",
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey.shade500,
                            // color: Color(0xff4F4F4F),
                          ),
                        ),
                        const VerticalDivider(),
                        Text(
                          !(order.cashOnDelivery ?? false) ? "Online" : "COD",
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey.shade500,
                            // color: Color(0xff4F4F4F),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
    /*  Container(
      padding: const EdgeInsets.symmetric(horizontal: 6.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order ID: ${order.orderId}',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 4.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Date: ${_formatDate(order.time!)}'),
              Text('Status: ${order.status}'),
            ],
          ),
        ],
      ),
    ); */
  }

  Widget _buildRiderDetails(RiderInOrderModel? rider, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),

      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Colors.white,
        // color: Color.fromARGB(255, 250, 250, 250),
        // boxShadow: [
        //   BoxShadow(
        //     color: const Color.fromARGB(19, 0, 0, 0),
        //     blurRadius: 10,
        //     spreadRadius: .8,
        //     offset: Offset(.2, 1.5),
        //   ),
        // ],
      ),
      // padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
      // decoration: const BoxDecoration(
      //   color: Color.fromARGB(255, 250, 250, 250),
      //   // boxShadow: [
      //   //   BoxShadow(
      //   //     color: Colors.black12,
      //   //     blurRadius: 1,
      //   //   )
      //   // ],
      // ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            clipBehavior: Clip.antiAlias,
            child: Center(
              child: SizedBox(
                height: 35,
                width: 35,
                child: Image.asset('assets/rider_logo.png', fit: BoxFit.cover),
              ),
            ),
          ),
          const SizedBox(width: 13),

          if (rider == null)
            const Text(
              'Rider will be assigned soon',
              style: TextStyle(
                color: Color(0xff4F4F4F),
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
          if (rider != null) ...[
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  rider.name,
                  style: const TextStyle(
                    color: Color(0xff4F4F4F),
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 3),
                Text(
                  "Delivery Partner",
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey.shade500,
                    // color: Color(0xff4F4F4F),
                  ),
                ),
              ],
            ),
            const Spacer(),
            Text(rider.number, style: TextStyle(fontSize: 13)),
            const SizedBox(width: 8),
            InkWell(
              onTap: () {},
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: const Icon(
                  CupertinoIcons.phone,
                  size: 20,
                  color: Colors.green,
                ),
              ),
            ),
          ],
          // Column(
          //   crossAxisAlignment: CrossAxisAlignment.end,
          //   children: [

          //   ],
          // ),
        ],
      ),
    );
  }

  Widget _buildFoodItemsList(
    List<FoodInOrderModel> foodItems,
    ThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),

      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Colors.white,
        // color: Color.fromARGB(255, 250, 250, 250),
        // boxShadow: [
        //   BoxShadow(
        //     color: const Color.fromARGB(19, 0, 0, 0),
        //     blurRadius: 10,
        //     spreadRadius: .8,
        //     offset: Offset(.2, 1.5),
        //   ),
        // ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image of the food item (replace with your image loading logic)
              Expanded(
                child: Text(
                  'ITEM',
                  style: TextStyle(
                    color: Color.fromARGB(255, 122, 122, 122),
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ), // Adjust style as needed
                ),
              ),
              SizedBox(width: 10),
              Container(
                width: 40,
                alignment: Alignment.center,

                child: Text(
                  'QTY',
                  style: TextStyle(
                    color: Color.fromARGB(255, 122, 122, 122),
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ), // Adjust style as needed
                ),
              ),
              SizedBox(width: 10),

              // Spacer(),

              // Add text for price including tax for each item here
              Container(
                width: 90,
                alignment: Alignment.centerRight,
                child: Text(
                  // Replace with price calculation including tax
                  'PRICE',
                  style: TextStyle(
                    color: Color.fromARGB(255, 122, 122, 122),
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ), // Adjust style as needed
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ListView.separated(
            separatorBuilder: (context, index) {
              return const Divider(thickness: .09, color: Colors.black);
            },
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: foodItems.length,
            itemBuilder: (context, index) {
              final foodItem = foodItems[index];
              return _buildFoodItemCard(
                foodItem,
                theme,
              ); // Placeholder for individual food item card
            },
          ),
          // const SizedBox(height: 16.0), // Add spacing after the list
        ],
      ),
    );
  }

  Widget _buildFoodItemCard(FoodInOrderModel foodItem, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(foodItem.name, style: theme.textTheme.bodyLarge),
                if (foodItem.variantName.isNotEmpty)
                  Text(
                    '(${foodItem.variantName})',
                    style: theme.textTheme.bodySmall,
                  ),
              ],
            ),
          ),
          const SizedBox(width: 10),

          Container(
            width: 40,
            alignment: Alignment.center,

            child: Text('${foodItem.qty}', style: theme.textTheme.bodySmall),
          ),
          const SizedBox(width: 10),
          Container(
            width: 90,
            alignment: Alignment.centerRight,
            child: Text(
              '₹ ${foodItem.price.toStringAsFixed(2)}',
              style: theme.textTheme.titleSmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderNote(String orderNote, ThemeData theme) {
    return Container(
      width: double.maxFinite,
      // padding: const EdgeInsets.all(16.0),
      // decoration: BoxDecoration(
      //   color: theme.primaryColorLight,
      //   borderRadius: BorderRadius.circular(8.0),
      // ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),

      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Color.fromARGB(255, 225, 242, 255),
        // color: Color.fromARGB(255, 250, 250, 250),
        // boxShadow: [
        //   BoxShadow(
        //     color: const Color.fromARGB(19, 0, 0, 0),
        //     blurRadius: 10,
        //     spreadRadius: .8,
        //     offset: Offset(.2, 1.5),
        //   ),
        // ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Note'.toUpperCase(),
            style: const TextStyle(
              color: Color.fromARGB(255, 122, 122, 122),
              fontWeight: FontWeight.w600,
              fontSize: 12.5,
            ),
          ),
          const SizedBox(height: 3.0),
          Text(
            orderNote,
            style: TextStyle(
              color: Color.fromARGB(255, 45, 45, 45),
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalPriceAndPaymentDetails(OrderModel order, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),

      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Colors.white,
        // color: Color.fromARGB(255, 250, 250, 250),
        // boxShadow: [
        //   BoxShadow(
        //     color: const Color.fromARGB(19, 0, 0, 0),
        //     blurRadius: 10,
        //     spreadRadius: .8,
        //     offset: Offset(.2, 1.5),
        //   ),
        // ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Payment Summary".toUpperCase(),
            style: const TextStyle(
              color: Color.fromARGB(255, 122, 122, 122),
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Sub Total', style: theme.textTheme.titleMedium),
              Text(
                // Replace with calculated total price
                '₹ ${(order.totalAmountPaid + order.rewardPointsUsed - order.totalTax).toStringAsFixed(2)}',
                style: theme.textTheme.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Reward Points Used', style: theme.textTheme.titleMedium),
              Text(
                // Replace with calculated total price
                '-₹ ${order.rewardPointsUsed.toStringAsFixed(2)}',
                style: theme.textTheme.titleMedium,
              ),
            ],
          ),
          if (order.discountCoupon != null) ...[
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Discount Amount', style: theme.textTheme.titleMedium),
                Text(
                  // Replace with calculated total price
                  '- ₹ ${(order.discountAmount - order.rewardPointsUsed).toStringAsFixed(2)}',
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
          ],
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Total Tax', style: theme.textTheme.titleMedium),
              Text(
                // Replace with calculated total price
                '+ ₹ ${order.totalTax.toStringAsFixed(2)}',
                style: theme.textTheme.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                // Replace with actual payment method (e.g., COD, Online)
                order.cashOnDelivery == true
                    ? 'Cash on Delivery'
                    : 'Online Payment',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: const Color(0xff4F4F4F),
                  fontWeight: FontWeight.w600,
                  fontSize: 15,
                ),
              ),
              Text(
                // Replace with calculated total price
                '₹ ${order.totalAmountPaid.toStringAsFixed(2)}',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: const Color(0xff4F4F4F),
                  fontWeight: FontWeight.bold,
                  fontSize: 15,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 5),
            decoration: BoxDecoration(
              color: const Color(0xffeef4ff),
              borderRadius: BorderRadius.circular(5),
              border: Border.all(color: const Color(0xff628cb9)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  // Replace with actual payment method (e.g., COD, Online)
                  'Reward Points Earned',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: const Color(0xff628cb9),
                  ),
                  // style: TextStyle(color: Color(0xff628cb9)),
                ),
                Text(
                  // Replace with calculated total price
                  order.rewardPointsEarned.toStringAsFixed(2),
                  style: const TextStyle(color: Color(0xff628cb9)),
                  // style: theme.textTheme.titleMedium,
                ),
              ],
            ),
          ),
          const SizedBox(height: 3),
        ],
      ),
    );
  }
}
