import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart'
    as polyline;
import 'package:get/get.dart';
import 'package:map_location_picker/map_location_picker.dart';
import 'package:swagat_user/controllers/home_controller.dart';
import 'package:swagat_user/models/order_models/order_model.dart';
import 'package:swagat_user/models/rider_model.dart';
import 'package:swagat_user/shared/const.dart';
import 'package:swagat_user/shared/firebase.dart';

class OrderLiveTraking extends StatefulWidget {
  const OrderLiveTraking({super.key, required this.order});
  final OrderModel order;

  @override
  State<OrderLiveTraking> createState() => _OrderLiveTrakingState();
}

class _OrderLiveTrakingState extends State<OrderLiveTraking> {
  final Completer<GoogleMapController> _controller = Completer();
  BitmapDescriptor? riderIcon;
  LatLng? sourceLocation;
  LatLng? destination;
  LatLng? riderLocation;
  List<LatLng> polylineCoordinates = [];
  @override
  void initState() {
    super.initState();
    getPolyPoints();
    setUp();
  }

  @override
  Widget build(BuildContext context) {
    return sourceLocation == null
        ? const Center(child: CircularProgressIndicator())
        : GoogleMap(
          initialCameraPosition: CameraPosition(
            target: sourceLocation!,
            zoom: 13.5,
          ),
          markers: {
            if (sourceLocation != null)
              Marker(
                markerId: const MarkerId("SWAGAT CORNER"),
                position: sourceLocation!,
              ),
            if (riderLocation != null)
              Marker(
                markerId: const MarkerId("RIDER"),
                position: riderLocation!,
                draggable: false,
                icon:
                    riderIcon ??
                    BitmapDescriptor.defaultMarkerWithHue(
                      BitmapDescriptor.hueViolet,
                    ),
              ),
            if (destination != null)
              Marker(
                markerId: const MarkerId("DESTINATION"),
                position: destination!,
              ),
          },
          onMapCreated: (mapController) {
            _controller.complete(mapController);
          },
          polylines: {
            Polyline(
              startCap: Cap.buttCap,
              polylineId: const PolylineId("route"),

              points: polylineCoordinates,
              color: const Color(0xFF7B61FF),
              // color: const Color.fromARGB(255, 114, 111, 131),
              jointType: JointType.round,
              width: 4,
              // width: 6,
            ),
          },
        );
  }

  setUp() async {
    try {
      destination = LatLng(
        widget.order.userAddress?.lat.toDouble() ?? 0,
        widget.order.userAddress?.long.toDouble() ?? 0,
      );
      sourceLocation = LatLng(
        Get.find<HomeCtrl>().currentOutlet?.lat.toDouble() ?? 0,
        Get.find<HomeCtrl>().currentOutlet?.long.toDouble() ?? 0,
      );
      riderIcon = await BitmapDescriptor.asset(
        const ImageConfiguration(size: Size(43, 48), devicePixelRatio: 2),
        'assets/rider.png',
      );
      setState(() {});
      getPolyPoints();
      if (widget.order.rider?.docId != null) riderDataStream();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void getPolyPoints() async {
    try {
      if (destination == null || sourceLocation == null) return;
      // print('DESTI - $destination');
      // print('SOURCE - $sourceLocation');
      polyline.PolylinePoints polylinePoints = polyline.PolylinePoints();
      polyline.PolylineResult result = await polylinePoints
          .getRouteBetweenCoordinates(
            request: polyline.PolylineRequest(
              origin: polyline.PointLatLng(
                sourceLocation!.latitude,
                sourceLocation!.longitude,
              ),
              destination: polyline.PointLatLng(
                destination!.latitude,
                destination!.longitude,
              ),
              mode: polyline.TravelMode.driving,
            ),
            // PointLatLng(sourceLocation!.latitude, sourceLocation!.longitude),
            // PointLatLng(destination!.latitude, destination!.longitude),
            googleApiKey: gkForNow,
          );
      if (result.points.isNotEmpty) {
        for (var point in result.points) {
          polylineCoordinates.add(LatLng(point.latitude, point.longitude));
        }
        setState(() {});
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  riderDataStream() async {
    try {
      GoogleMapController googleMapController = await _controller.future;
      FBFireStore.rider.doc(widget.order.rider?.docId).snapshots().listen((
        event,
      ) {
        final riderData = RiderModel.fromDocSnap(event);
        // Set new location
        riderLocation = LatLng(
          riderData.lat?.toDouble() ?? 0,
          riderData.lng?.toDouble() ?? 0,
        );
        // print('RIDER - $riderLocation');
        // Animate camara
        googleMapController.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(zoom: 15.5, target: riderLocation!),
          ),
        );
        setState(() {});
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
