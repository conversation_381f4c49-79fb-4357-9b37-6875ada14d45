import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:swagat_user/models/order_models/order_model.dart';
import 'package:swagat_user/models/outlet_model.dart';
import 'package:swagat_user/shared/const.dart';
import 'package:swagat_user/shared/firebase.dart';
import 'package:swagat_user/shared/methods.dart';
import 'package:swagat_user/shared/theme.dart';
import '../../models/order_models/food_in_order_model.dart';

class OrderDetails extends StatefulWidget {
  const OrderDetails({
    super.key,
    required this.orderModel,
    required this.orderId,
  });
  final String orderId;
  final OrderModel orderModel;
  @override
  State<OrderDetails> createState() => _OrderDetailsState();
}

class _OrderDetailsState extends State<OrderDetails> {
  OutletModel? outlet;
  List<FoodInOrderModel> foodItems = [];
  num itemTotal = 0;
  bool fetchingData = false;
  @override
  void initState() {
    super.initState();
    fetchExtraData();
  }

  fetchExtraData() async {
    fetchingData = true;
    setState(() {});
    outlet = await FBFireStore.outlets
        .doc(widget.orderModel.outletDocId)
        .get()
        .then((value) => OutletModel.fromDocSnap(value));
    foodItems = widget.orderModel.foodItems;
    num itemSum = 0;
    for (var item in foodItems) {
      itemSum += (item.qty * item.price);
    }
    itemTotal = itemSum;
    fetchingData = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeData(useMaterial3: true, colorSchemeSeed: themeColor),
      child: Scaffold(
        backgroundColor: const Color(0xfff5f6fb),
        appBar: AppBar(
          backgroundColor: Colors.white,
          surfaceTintColor: Colors.white,
          elevation: 1,
          scrolledUnderElevation: 5,
          titleSpacing: 4,
          shadowColor: Colors.black45,
          centerTitle: false,
          title: const Text('Order Summary'),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 12.0),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.redAccent,
                  foregroundColor: Colors.white,
                ),
                onPressed: () {},
                child: const Text(
                  'Invoice',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
        body:
            fetchingData
                ? Center(
                  child: CircularProgressIndicator(
                    color: themeColor.withOpacity(.9),
                  ),
                )
                : SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(
                    vertical: 15,
                    horizontal: 15,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children:
                        true
                            ? [
                              Container(
                                width: double.maxFinite,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(7),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: Row(
                                    children: [
                                      Container(
                                        height: 40,
                                        width: 40,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          shape: BoxShape.circle,
                                        ),
                                        clipBehavior: Clip.antiAlias,
                                        child: Center(
                                          child: Image.asset(
                                            'assets/swagat1.png',
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 10),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text.rich(
                                            TextSpan(
                                              children: [
                                                const TextSpan(
                                                  text: 'Swagat Corner, ',
                                                  style: TextStyle(
                                                    // color: Color.fromARGB(255, 86, 86, 86),
                                                    color: Color.fromARGB(
                                                      255,
                                                      59,
                                                      58,
                                                      58,
                                                    ),
                                                    fontWeight: FontWeight.w500,
                                                    fontSize: 17,
                                                  ),
                                                ),
                                                TextSpan(
                                                  text:
                                                      outlet?.outletName ?? "",
                                                  style: const TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w500,
                                                    color: Color.fromARGB(
                                                      255,
                                                      59,
                                                      58,
                                                      58,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Text(outlet?.outletAddresss ?? ""),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              SizedBox(height: 15),

                              Container(
                                width: double.maxFinite,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(7),
                                ),
                                child: Column(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.all(10.0),

                                      child: Row(
                                        children: [
                                          Container(
                                            height: 40,
                                            width: 40,
                                            decoration: BoxDecoration(
                                              color: Color(0xfff5f6fb),
                                              shape: BoxShape.circle,
                                            ),
                                            clipBehavior: Clip.antiAlias,
                                            child: Center(
                                              child: Icon(
                                                CupertinoIcons.bag,
                                                size: 22,
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 10),
                                          Text(
                                            'Order Items',
                                            style: TextStyle(
                                              // color: Color.fromARGB(255, 86, 86, 86),
                                              color: Color.fromARGB(
                                                255,
                                                59,
                                                58,
                                                58,
                                              ),
                                              fontWeight: FontWeight.w500,
                                              fontSize: 17,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Divider(
                                      height: 0,
                                      thickness: .07,
                                      color: Colors.grey,
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(10),
                                      child: ListView.separated(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemCount: foodItems.length,
                                        separatorBuilder: (context, index) {
                                          return SizedBox(height: 20);
                                        },

                                        itemBuilder: (context, index) {
                                          return Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text.rich(
                                                TextSpan(
                                                  children: [
                                                    TextSpan(
                                                      text:
                                                          foodItems[index].name,
                                                      style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontSize: 15,
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text:
                                                          ', ${foodItems[index].variantName}',
                                                      style: const TextStyle(
                                                        fontSize: 13,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              Row(
                                                children: [
                                                  Text(
                                                    foodItems[index].qty
                                                        .toString(),
                                                  ),
                                                  const SizedBox(width: 3),
                                                  const Text('x'),
                                                  const SizedBox(width: 3),
                                                  Text(
                                                    '₹${foodItems[index].price.toStringAsFixed(2)}',
                                                  ),
                                                  const Spacer(),
                                                  Text(
                                                    '₹${(foodItems[index].qty * foodItems[index].price).toStringAsFixed(2)}',
                                                  ),
                                                ],
                                              ),
                                            ],
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 15),

                              Container(
                                width: double.maxFinite,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(7),
                                ),
                                child: Column(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.all(10.0),

                                      child: Row(
                                        children: [
                                          Container(
                                            height: 40,
                                            width: 40,
                                            decoration: BoxDecoration(
                                              color: Color(0xfff5f6fb),
                                              shape: BoxShape.circle,
                                            ),
                                            clipBehavior: Clip.antiAlias,
                                            child: Center(
                                              child: Icon(
                                                CupertinoIcons.doc_text,
                                                size: 22,
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 10),
                                          Text(
                                            'Bill Summary',
                                            style: TextStyle(
                                              // color: Color.fromARGB(255, 86, 86, 86),
                                              color: Color.fromARGB(
                                                255,
                                                59,
                                                58,
                                                58,
                                              ),
                                              fontWeight: FontWeight.w500,
                                              fontSize: 17,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Divider(
                                      height: 0,
                                      thickness: .07,
                                      color: Colors.grey,
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: Column(
                                        children: [
                                          Row(
                                            children: [
                                              Text(
                                                'Item Total',
                                                style: TextStyle(
                                                  // color: Color.fromARGB(255, 86, 86, 86),
                                                  color: Color.fromARGB(
                                                    255,
                                                    127,
                                                    130,
                                                    133,
                                                  ),
                                                  fontWeight: FontWeight.w400,
                                                  fontSize: 15,
                                                ),
                                              ),
                                              Spacer(),
                                              Text(
                                                '₹${itemTotal.toStringAsFixed(2)}',
                                                style: TextStyle(
                                                  // color: Color.fromARGB(255, 86, 86, 86),
                                                  color: Color.fromARGB(
                                                    255,
                                                    59,
                                                    58,
                                                    58,
                                                  ),
                                                  fontWeight: FontWeight.w400,
                                                  fontSize: 15,
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 10),
                                          Row(
                                            children: [
                                              Text(
                                                'Tax',
                                                style: TextStyle(
                                                  // color: Color.fromARGB(255, 86, 86, 86),
                                                  color: Color.fromARGB(
                                                    255,
                                                    127,
                                                    130,
                                                    133,
                                                  ),
                                                  fontWeight: FontWeight.w400,
                                                  fontSize: 15,
                                                ),
                                              ),
                                              Spacer(),
                                              Text(
                                                '+ ₹${widget.orderModel.totalTax.toStringAsFixed(2)}',
                                                style: TextStyle(
                                                  // color: Color.fromARGB(255, 86, 86, 86),
                                                  color: Color.fromARGB(
                                                    255,
                                                    59,
                                                    58,
                                                    58,
                                                  ),
                                                  fontWeight: FontWeight.w400,
                                                  fontSize: 15,
                                                ),
                                              ),
                                            ],
                                          ),

                                          if (widget
                                                  .orderModel
                                                  .rewardPointsUsed !=
                                              0) ...[
                                            SizedBox(height: 10),
                                            Row(
                                              children: [
                                                Text(
                                                  'Points Used',
                                                  style: TextStyle(
                                                    // color: Color.fromARGB(255, 86, 86, 86),
                                                    color: Color.fromARGB(
                                                      255,
                                                      127,
                                                      130,
                                                      133,
                                                    ),
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 15,
                                                  ),
                                                ),
                                                Spacer(),
                                                Text(
                                                  '- ₹${(widget.orderModel.rewardPointsUsed).toStringAsFixed(2)}',
                                                  style: TextStyle(
                                                    // color: Color.fromARGB(255, 86, 86, 86),
                                                    color: Color.fromARGB(
                                                      255,
                                                      59,
                                                      58,
                                                      58,
                                                    ),
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 15,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                          if (widget.orderModel.discountAmount -
                                                  widget
                                                      .orderModel
                                                      .rewardPointsUsed !=
                                              0) ...[
                                            SizedBox(height: 10),

                                            Row(
                                              children: [
                                                Text(
                                                  'Discount Amount',
                                                  style: TextStyle(
                                                    // color: Color.fromARGB(255, 86, 86, 86),
                                                    color: Color.fromARGB(
                                                      255,
                                                      127,
                                                      130,
                                                      133,
                                                    ),
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 15,
                                                  ),
                                                ),
                                                Spacer(),
                                                Text(
                                                  '- ₹${(widget.orderModel.discountAmount - widget.orderModel.rewardPointsUsed).toStringAsFixed(2)}',
                                                  style: TextStyle(
                                                    // color: Color.fromARGB(255, 86, 86, 86),
                                                    color: Color.fromARGB(
                                                      255,
                                                      59,
                                                      58,
                                                      58,
                                                    ),
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 15,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                          SizedBox(height: 25),
                                          Row(
                                            children: [
                                              Text(
                                                'Total',
                                                style: TextStyle(
                                                  // color: Color.fromARGB(255, 86, 86, 86),
                                                  color: Color.fromARGB(
                                                    255,
                                                    59,
                                                    58,
                                                    58,
                                                  ),
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 15,
                                                ),
                                              ),
                                              Spacer(),
                                              Text(
                                                '+ ₹${widget.orderModel.totalAmountPaid.toStringAsFixed(2)}',
                                                style: TextStyle(
                                                  // color: Color.fromARGB(255, 86, 86, 86),
                                                  color: Color.fromARGB(
                                                    255,
                                                    59,
                                                    58,
                                                    58,
                                                  ),
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 15,
                                                ),
                                              ),
                                            ],
                                          ),
                                          SizedBox(height: 15),
                                          Container(
                                            padding: EdgeInsets.symmetric(
                                              vertical: 5,
                                              horizontal: 3,
                                            ),
                                            decoration: BoxDecoration(
                                              color: Color(0xffdbe7fe),
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                            ),
                                            child: Center(
                                              child: Text(
                                                'Reward points earned : ${widget.orderModel.rewardPointsEarned.toStringAsFixed(2)}',
                                                style: TextStyle(
                                                  // color: Color.fromARGB(255, 86, 86, 86),
                                                  color: Color(0xff457cce),
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 15,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 15),

                              Container(
                                width: double.maxFinite,
                                padding: const EdgeInsets.all(10.0),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(7),
                                ),
                                child: Column(
                                  children: [
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          height: 32,
                                          width: 40,
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(
                                            CupertinoIcons.person,
                                            size: 23,
                                            // size: 35,
                                          ),
                                        ),
                                        const SizedBox(width: 5),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              widget
                                                      .orderModel
                                                      .userData
                                                      ?.name ??
                                                  '-',
                                              style: TextStyle(
                                                // color: Color.fromARGB(255, 86, 86, 86),
                                                color: Color.fromARGB(
                                                  255,
                                                  59,
                                                  58,
                                                  58,
                                                ),
                                                fontWeight: FontWeight.w500,
                                                fontSize: 15,
                                              ),
                                            ),
                                            Text(
                                              widget
                                                      .orderModel
                                                      .userData
                                                      ?.number ??
                                                  '-',
                                              style: TextStyle(
                                                // color: Color.fromARGB(255, 86, 86, 86),
                                                color: Color(0xff8d8e94),
                                                fontWeight: FontWeight.w400,
                                                fontSize: 15,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    if (widget.orderModel.type !=
                                        OrderType.takeAway) ...[
                                      SizedBox(height: 25),
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Container(
                                            height: 32,
                                            width: 40,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              shape: BoxShape.circle,
                                            ),
                                            child: Icon(
                                              CupertinoIcons.creditcard,
                                              size: 23,

                                              // size: 35,
                                            ),
                                          ),
                                          const SizedBox(width: 5),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Payment method',
                                                style: TextStyle(
                                                  // color: Color.fromARGB(255, 86, 86, 86),
                                                  color: Color.fromARGB(
                                                    255,
                                                    59,
                                                    58,
                                                    58,
                                                  ),
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 15,
                                                ),
                                              ),

                                              Text(
                                                widget
                                                            .orderModel
                                                            .cashOnDelivery ??
                                                        false
                                                    ? 'Cash on delivery'
                                                    : 'Paid via: ${widget.orderModel.paymentMethod == null
                                                        ? '-'
                                                        : widget.orderModel.paymentMethod?.keys.first == 'card'
                                                        ? 'Card'
                                                        : widget.orderModel.paymentMethod?.values.first['channel'] ?? '-'}',
                                                style: TextStyle(
                                                  // color: Color.fromARGB(255, 86, 86, 86),
                                                  color: Color(0xff8d8e94),
                                                  fontWeight: FontWeight.w400,
                                                  fontSize: 15,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),

                                      SizedBox(height: 25),
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Container(
                                            height: 32,
                                            width: 40,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              shape: BoxShape.circle,
                                            ),
                                            child: Icon(
                                              CupertinoIcons.calendar,
                                              size: 23,

                                              // size: 35,
                                            ),
                                          ),
                                          const SizedBox(width: 5),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Payment date',
                                                style: TextStyle(
                                                  // color: Color.fromARGB(255, 86, 86, 86),
                                                  color: Color.fromARGB(
                                                    255,
                                                    59,
                                                    58,
                                                    58,
                                                  ),
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 15,
                                                ),
                                              ),
                                              if (widget.orderModel.paidOn !=
                                                  null)
                                                Text(
                                                  '${widget.orderModel.paidOn?.toDate().goodDayDate()} at ${widget.orderModel.paidOn?.toDate().goodTime()}',
                                                  style: TextStyle(
                                                    // color: Color.fromARGB(255, 86, 86, 86),
                                                    color: Color(0xff8d8e94),
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 15,
                                                  ),
                                                ),
                                              if (widget.orderModel.paidOn ==
                                                  null)
                                                Text(
                                                  "-",
                                                  style: TextStyle(
                                                    // color: Color.fromARGB(255, 86, 86, 86),
                                                    color: Color(0xff8d8e94),
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 15,
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ],
                                    SizedBox(height: 25),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          height: 32,
                                          width: 40,
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            shape: BoxShape.circle,
                                          ),
                                          child: Icon(
                                            CupertinoIcons.placemark,
                                            size: 23,

                                            // size: 35,
                                          ),
                                        ),
                                        const SizedBox(width: 5),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Delivery address',
                                                style: TextStyle(
                                                  // color: Color.fromARGB(255, 86, 86, 86),
                                                  color: Color.fromARGB(
                                                    255,
                                                    59,
                                                    58,
                                                    58,
                                                  ),
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 15,
                                                ),
                                              ),

                                              if (widget.orderModel.type ==
                                                  "Delivery")
                                                Text(
                                                  widget
                                                              .orderModel
                                                              .userAddress !=
                                                          null
                                                      ? '${widget.orderModel.userAddress?.flat}, ${widget.orderModel.userAddress?.area}, ${widget.orderModel.userAddress?.city}'
                                                      : '-',
                                                  maxLines: 2,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  style: const TextStyle(
                                                    color: Color(0xff8d8e94),
                                                    fontWeight: FontWeight.w400,
                                                    fontSize: 15,
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    // SizedBox(height: 15),
                                  ],
                                ),
                              ),
                            ]
                            : [
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 3.0,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text.rich(
                                      TextSpan(
                                        children: [
                                          const TextSpan(
                                            text: 'Swagat Corner, ',
                                            style: TextStyle(
                                              // color: Color.fromARGB(255, 86, 86, 86),
                                              color: Color.fromARGB(
                                                255,
                                                59,
                                                58,
                                                58,
                                              ),
                                              fontWeight: FontWeight.w500,
                                              fontSize: 17,
                                            ),
                                          ),
                                          TextSpan(
                                            text: outlet?.outletName ?? "",
                                            style: const TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w500,
                                              color: Color.fromARGB(
                                                255,
                                                59,
                                                58,
                                                58,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Text(outlet?.outletAddresss ?? ""),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 17),
                              Container(
                                clipBehavior: Clip.antiAlias,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4),
                                  color: Colors.white,
                                  boxShadow: [
                                    BoxShadow(
                                      blurRadius: 2,
                                      color: Colors.grey.shade300,
                                      offset: const Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(10),
                                      decoration: BoxDecoration(
                                        color: themeColor.withOpacity(.91),
                                        // borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: const Row(
                                        children: [
                                          Text(
                                            "Order Items",
                                            style: TextStyle(
                                              fontSize: 15.5,
                                              color: Colors.white,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          Spacer(),
                                          Icon(
                                            CupertinoIcons.chevron_down,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 5),
                                    // const Divider(
                                    //   color: Colors.grey,
                                    //   thickness: 1.5,
                                    // ),
                                    // Text(foodItems.length.toString()),
                                    Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: ListView.separated(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        itemCount: foodItems.length,
                                        separatorBuilder: (context, index) {
                                          return const Divider(
                                            color: Colors.grey,
                                            thickness: .07,
                                          );
                                        },
                                        itemBuilder: (context, index) {
                                          return Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text.rich(
                                                TextSpan(
                                                  children: [
                                                    TextSpan(
                                                      text:
                                                          foodItems[index].name,
                                                      style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        fontSize: 15,
                                                      ),
                                                    ),
                                                    TextSpan(
                                                      text:
                                                          ', ${foodItems[index].variantName}',
                                                      style: const TextStyle(
                                                        fontSize: 13,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              Row(
                                                children: [
                                                  Text(
                                                    foodItems[index].qty
                                                        .toString(),
                                                  ),
                                                  const SizedBox(width: 3),
                                                  const Text('x'),
                                                  const SizedBox(width: 3),
                                                  Text(
                                                    foodItems[index].price
                                                        .toStringAsFixed(2),
                                                  ),
                                                  const Spacer(),
                                                  Text(
                                                    '₹${(foodItems[index].qty * foodItems[index].price).toStringAsFixed(2)}',
                                                  ),
                                                ],
                                              ),
                                            ],
                                          );
                                        },
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                        8,
                                        0,
                                        8,
                                        8,
                                      ),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Divider(
                                            thickness: .2,
                                            color: Colors.grey.shade400,
                                          ),
                                          Row(
                                            children: [
                                              const Text(
                                                "Item Total",
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 15,
                                                ),
                                              ),
                                              const Spacer(),
                                              Text(
                                                '₹${itemTotal.toStringAsFixed(2)}',
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 15,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 4),
                                          Row(
                                            children: [
                                              const Text(
                                                "Tax",
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xff4F4F4F),
                                                ),
                                              ),
                                              const Spacer(),
                                              Text(
                                                '+ ₹${widget.orderModel.totalTax.toStringAsFixed(2)}',
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xff4F4F4F),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 4),
                                          Row(
                                            children: [
                                              const Text(
                                                "Reward points used",
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xff4F4F4F),
                                                ),
                                              ),
                                              const Spacer(),
                                              Text(
                                                '- ₹${widget.orderModel.rewardPointsUsed.toStringAsFixed(2)}',
                                                style: const TextStyle(
                                                  fontSize: 12,
                                                  color: Color(0xff4F4F4F),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 4),
                                          if (widget
                                                  .orderModel
                                                  .discountCoupon !=
                                              null)
                                            Row(
                                              children: [
                                                const Text(
                                                  "Discount amount",
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: Color(0xff4F4F4F),
                                                  ),
                                                ),
                                                const Spacer(),
                                                Text(
                                                  '- ₹${(widget.orderModel.discountAmount - widget.orderModel.rewardPointsUsed).toStringAsFixed(2)}',
                                                  style: const TextStyle(
                                                    fontSize: 12,
                                                    color: Color(0xff4F4F4F),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          if (widget
                                                  .orderModel
                                                  .discountCoupon !=
                                              null)
                                            const SizedBox(height: 4),
                                          Row(
                                            children: [
                                              const Text(
                                                "Grand Total",
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              const Spacer(),
                                              Text(
                                                '₹${widget.orderModel.totalAmountPaid.toStringAsFixed(2)}',
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 16,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 25),
                              Container(
                                clipBehavior: Clip.antiAlias,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4),
                                  color: Colors.white,
                                  boxShadow: [
                                    BoxShadow(
                                      blurRadius: 2,
                                      color: Colors.grey.shade300,
                                      offset: const Offset(0, .9),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(10),
                                      decoration: BoxDecoration(
                                        color: themeColor.withOpacity(.91),
                                        // borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: const Row(
                                        children: [
                                          Text(
                                            "Order Details",
                                            style: TextStyle(
                                              fontSize: 15.5,
                                              color: Colors.white,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          Spacer(),
                                          Icon(
                                            CupertinoIcons.chevron_down,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(height: 5),
                                    Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const Text(
                                            "Order Id",
                                            style: TextStyle(
                                              color: Color(0xff4F4F4F),
                                              // fontWeight: FontWeight.w500,
                                              fontSize: 14,
                                            ),
                                          ),
                                          Text(
                                            widget.orderModel.orderId,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.w500,
                                              fontSize: 15,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          const Text(
                                            "Payment",
                                            style: TextStyle(
                                              color: Color(0xff4F4F4F),
                                              // fontWeight: FontWeight.w500,
                                              fontSize: 14,
                                            ),
                                          ),
                                          Text(
                                            widget.orderModel.isPaid
                                                ? 'Paid'
                                                : "Incomplete Payment",
                                            style: const TextStyle(
                                              fontWeight: FontWeight.w500,
                                              fontSize: 15,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          const Text(
                                            "Date",
                                            style: TextStyle(
                                              color: Color(0xff4F4F4F),
                                              // fontWeight: FontWeight.w500,
                                              fontSize: 14,
                                            ),
                                          ),
                                          Text(
                                            widget.orderModel.time != null
                                                ? '${widget.orderModel.time!.toDate().goodDayDate()} at ${widget.orderModel.time!.toDate().goodTime()}'
                                                : "-",
                                            style: const TextStyle(
                                              fontWeight: FontWeight.w500,
                                              fontSize: 15,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          const Text(
                                            "Mobile Number",
                                            style: TextStyle(
                                              color: Color(0xff4F4F4F),
                                              // fontWeight: FontWeight.w500,
                                              fontSize: 14,
                                            ),
                                          ),
                                          Text(
                                            widget
                                                    .orderModel
                                                    .userData
                                                    ?.number ??
                                                "-",
                                            style: const TextStyle(
                                              fontWeight: FontWeight.w500,
                                              fontSize: 15,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          const Text(
                                            "Type",
                                            style: TextStyle(
                                              color: Color(0xff4F4F4F),
                                              // fontWeight: FontWeight.w500,
                                              fontSize: 14,
                                            ),
                                          ),
                                          Text(
                                            widget.orderModel.type,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.w500,
                                              fontSize: 15,
                                            ),
                                          ),
                                          if (widget.orderModel.type ==
                                              "Delivery")
                                            const SizedBox(height: 8),
                                          if (widget.orderModel.type ==
                                              "Delivery")
                                            const Text(
                                              "Delivery Address",
                                              style: TextStyle(
                                                color: Color(0xff4F4F4F),
                                                // fontWeight: FontWeight.w500,
                                                fontSize: 14,
                                              ),
                                            ),
                                          if (widget.orderModel.type ==
                                              "Delivery")
                                            Text(
                                              widget.orderModel.userAddress !=
                                                      null
                                                  ? '${widget.orderModel.userAddress?.flat}, ${widget.orderModel.userAddress?.area}, ${widget.orderModel.userAddress?.city}'
                                                  : '-',
                                              style: const TextStyle(
                                                fontWeight: FontWeight.w500,
                                                fontSize: 15,
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 8),
                            ],
                  ),
                ),
      ),
    );
  }
}
