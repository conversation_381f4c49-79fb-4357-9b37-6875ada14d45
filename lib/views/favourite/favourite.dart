// ignore_for_file: no_wildcard_variable_uses

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/home_controller.dart';
import '../homepage/homepage_widegts/appbar_homepage.dart';
import '../homepage/homepage_widegts/special_item_widget.dart';

class FavouriteScreen extends StatelessWidget {
  const FavouriteScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeCtrl>(
      builder: (ctrl) {
        final filtered =
            ctrl.foods
                .where(
                  (element) =>
                      (ctrl.currentUserData?.favourite.contains(
                            element.docId,
                          ) ??
                          false) &&
                      (element.name.toLowerCase().contains(
                        ctrl.searchController2.text.toLowerCase(),
                      )),
                )
                .toList();
        return Scaffold(
          backgroundColor: Colors.white,
          body: GestureDetector(
            onTap: () => FocusScope.of(context).unfocus(),
            child: CustomScrollView(
              slivers: <Widget>[
                // A P P  B A R
                HomepageAppbar(
                  extraHeight: 6,
                  pinned: true,
                  searchController: ctrl.searchController2,
                ),

                // B O D Y
                SliverToBoxAdapter(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Padding(
                        padding: EdgeInsets.fromLTRB(14, 10, 14, 0),
                        child: Text(
                          "Favourites",
                          style: TextStyle(fontSize: 25),
                        ),
                      ),
                      filtered.isEmpty
                          ? const Center(child: Text("No Favourites Selected"))
                          : ListView.separated(
                            padding: EdgeInsets.zero,
                            itemCount: filtered.length,
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              return SpecialItemCard(food: filtered[index]);
                            },
                            separatorBuilder: (
                              BuildContext context,
                              int index,
                            ) {
                              return const Padding(
                                padding: EdgeInsets.symmetric(horizontal: 12.0),
                                child: Divider(thickness: .2),
                              );
                            },
                          ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
