import 'package:another_flushbar/flushbar.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:swagat_user/shared/router.dart';

class AuthCtrl extends GetxController {
  late final TextEditingController phoneCtrl;
  final otpCtrl = TextEditingController();
  String verificationCode = "";
  int? resendToken;
  bool timout = false;
  bool sendingOtp = false;
  bool verifyingOtp = false;
  RxInt timerValue = 60.obs;

  @override
  void onInit() {
    super.onInit();
    phoneCtrl = TextEditingController();
  }

  resetTimer() => timerValue.value = 60;

  startTimer() async {
    while (timerValue > 0) {
      await Future.delayed(const Duration(seconds: 1));
      --timerValue.value;
    }
  }

  sentOtp(BuildContext context) async {
    try {
      if (sendingOtp) return;
      if (phoneCtrl.text.substring(0, 3) != "+91") {
        phoneCtrl.text = '+91${phoneCtrl.text}';
      }
      if (phoneCtrl.text.length < 10) {
        showAppSnackBar("Invalid Phone Number");
      }
      sendingOtp = true;
      update();
      await FirebaseAuth.instance.verifyPhoneNumber(
        forceResendingToken: resendToken,
        phoneNumber: phoneCtrl.text,
        verificationCompleted: (PhoneAuthCredential credential) async {
          debugPrint("Success");
          sendingOtp = false;
          update();
          await signInWithCreds(credential, context);
        },
        verificationFailed: (FirebaseAuthException e) {
          debugPrint('Failed $e');
          if (e.code == 'invalid-phone-number') {
            debugPrint('Phone number invalid');
          }
          sendingOtp = false;
          update();
        },
        codeSent: (String verificationId, int? resendToken) async {
          debugPrint("Code Sent... $verificationId");
          verificationCode = verificationId;
          this.resendToken = resendToken;
          sendingOtp = false;
          update();
          resetTimer();
          startTimer();
          // add navigator to otp
          if (context.mounted) context.go(Routes.otp);
        },
        timeout: const Duration(seconds: 60),
        codeAutoRetrievalTimeout: (String verificationId) {
          sendingOtp = false;
          update();
          debugPrint("Timeout...");
          timout = true;
        },
      );
    } catch (e) {
      debugPrint(e.toString());
      sendingOtp = false;
      showAppSnackBar(e.toString());
      // ScaffoldMessenger.of(
      //   context,
      // ).showSnackBar(SnackBar(content: Text(e.toString())));
      update();
    }
  }

  createCredsAndSignIn(BuildContext context) async {
    verifyingOtp = true;
    update();
    await signInWithCreds(createCreds(), context);
  }

  signInWithCreds(PhoneAuthCredential credential, BuildContext context) async {
    try {
      await FirebaseAuth.instance.signInWithCredential(credential);
      verifyingOtp = false;
      update();
      if (context.mounted) context.go(Routes.wrapper);
    } catch (e) {
      debugPrint(e.toString());
      verifyingOtp = false;
      update();
    }
  }

  PhoneAuthCredential createCreds() {
    return PhoneAuthProvider.credential(
      verificationId: verificationCode,
      smsCode: otpCtrl.text,
    );
  }
}

void showAppSnackBar(String message) {
  try {
    final cntx = appRouter.routerDelegate.navigatorKey.currentContext;
    if (cntx == null) {
      return;
    }
    Flushbar(
      message: message,
      flushbarPosition: FlushbarPosition.BOTTOM,
      flushbarStyle: FlushbarStyle.GROUNDED,
      duration: const Duration(seconds: 2, milliseconds: 500),
    ).show(cntx);
    return;
  } on Exception catch (e) {
    debugPrint(e.toString());
  }
}
