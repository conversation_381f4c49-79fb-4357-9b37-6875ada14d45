import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:swagat_user/controllers/auth_controller.dart';
import 'package:swagat_user/models/address_model.dart';
import 'package:swagat_user/models/order_models/order_model.dart';
import '../models/add_on_model.dart';
import '../models/categories_model.dart';
import '../models/discount_coupon.dart';
import '../models/food_model.dart';
import '../models/outlet_model.dart';
import '../models/user_model.dart';
import '../shared/const.dart';
import '../shared/firebase.dart';
import '../shared/methods.dart';

class HomeCtrl extends GetxController {
  bool fcmTokenChecked = false;
  int selectedIndex = 0;
  num subTotal = 0;
  num subTotalBeforeDis = 0;
  num totalTax = 0;
  num discountAmount = 0;
  num grandTotal = 0;
  num rewardPointsUsed = 0;
  num rewardPointsEarned = 0;
  String selectedOrderType = OrderType.delivery;
  bool cod = false;
  DiscountCouponModel? selectedCoupon;
  TextEditingController searchController1 = TextEditingController();
  TextEditingController searchController2 = TextEditingController();
  TextEditingController searchController3 = TextEditingController();
  TextEditingController orderNoteCtrl = TextEditingController();
  UserModel? currentUserData;
  OutletModel? currentOutlet;
  List<DiscountCouponModel> coupons = <DiscountCouponModel>[];
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? currentUserStream;
  List<CategoryModel> categories = <CategoryModel>[];
  List<FoodModel> foods = <FoodModel>[];
  List<OrderModel> currentOrders = <OrderModel>[];
  List<AddOnModel> addOns = <AddOnModel>[];
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? catsStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? foodsStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? currentOrdersStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? addOnsStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? adminsStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? couponsStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>?
  currentOutletStream;
  final cartItems = <UserCartItem>[].obs;
  AddressModel? selectedAddress;
  bool plusMinusAllowed = true;
  String? boolvariantId;
  bool valCoupon = false;
  @override
  void onInit() {
    super.onInit();
    fbUserStream();
    // allOutletStream();
    foodTimeChecker();
  }

  fbUserStream() async {
    try {
      FBAuth.auth.authStateChanges().listen((event) {
        if (event != null) currentUserDataStream();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  currentUserDataStream() async {
    try {
      currentUserStream?.cancel();
      currentUserStream = FirebaseFirestore.instance
          .collection('users')
          .doc(FBAuth.auth.currentUser!.uid)
          .snapshots()
          .listen((event) async {
            // print(event.size)
            if (event.data() != null) {
              currentUserData = UserModel.fromDocSnap(event);
              selectedAddress ??= currentUserData?.addresses.firstWhereOrNull(
                (element) => element.id == currentUserData?.defaultAddressId,
              );

              await setOutletStream();
              await setUpCart();
              // TODO: Active orders stream
              await currentOrderStream();
              await setFcmToken(event.data()?['tokens'] ?? []);
            } else {
              currentUserData = null;
              setNewUserData();
            }
            // update();
            calcCart();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  setUpCart() async {
    try {
      selectedCoupon = null;
      await Future.delayed(Duration(milliseconds: 800));
      cartItems.clear();
      currentUserData?.userCartItems.forEach((item) {
        final itemInCart = cartItems.firstWhereOrNull(
          (element) =>
              element.foodDocId == item.foodDocId &&
              element.variantId == item.variantId &&
              listsAreSame(element.addonDocIdList, item.addonDocIdList),
        );
        if (itemInCart == null) {
          addToCart(
            item.foodDocId,
            item.variantId,
            item.addonDocIdList,
            userCartItem: item,
          );
        }
      });

      calcCart();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  setOutletStream() async {
    try {
      currentOutletStream?.cancel();
      currentOutletStream = FBFireStore.outlets
          .doc(currentUserData?.selectedOutletDocId)
          .snapshots()
          .listen((event) async {
            if (event.data() != null) {
              currentOutlet = OutletModel.fromDocSnap(event);
              await categoryStream();
              await foodStream();
              await addOnStream();
              await couponStream();
              // Check Order Type
              checkOrdrAndPymtType();
              await FirebaseMessaging.instance.subscribeToTopic(
                currentOutlet?.outletName.removeAllWhitespace ?? "global",
              );
            } else {
              currentOutlet = null;
            }
            update();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  checkOrdrAndPymtType() {
    try {
      if (!isOutletActive) return;
      if (currentOutlet?.delivery == false &&
          selectedOrderType == OrderType.delivery) {
        selectedOrderType = OrderType.takeAway;
      }
      if (currentOutlet?.takeAway == false &&
          selectedOrderType == OrderType.takeAway) {
        selectedOrderType = OrderType.delivery;
      }
      if (cod == true && currentOutlet?.cashOnDelivery == false) cod = false;
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  bool get isOutletActive =>
      currentOutlet?.delivery == true || currentOutlet?.takeAway == true;

  couponStream() async {
    try {
      couponsStream?.cancel();
      couponsStream = FBFireStore.outlets
          .doc(currentUserData?.selectedOutletDocId)
          .collection('coupons')
          .where('blocked', isEqualTo: false)
          .snapshots()
          .listen((event) {
            coupons =
                event.docs.map((e) => DiscountCouponModel.fromSnap(e)).toList();
            // update();
            calcCart();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  categoryStream() async {
    try {
      catsStream?.cancel();
      catsStream = FBFireStore.outlets
          .doc(currentUserData?.selectedOutletDocId)
          .collection('category')
          .where("blocked", isEqualTo: false)
          .snapshots()
          .listen((event) {
            categories =
                event.docs.map((e) => CategoryModel.fromSnap(e)).toList();
            categories.sort((a, b) => a.priorityNo.compareTo(b.priorityNo));
            update();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  foodStream() async {
    try {
      foodsStream?.cancel();
      foodsStream = FBFireStore.outlets
          .doc(currentUserData?.selectedOutletDocId)
          .collection('food')
          .where('blocked', isEqualTo: false)
          .snapshots()
          .listen((event) {
            foods = event.docs.map((e) => FoodModel.fromSnap(e)).toList();
            checkFoodTimeFrames();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  currentOrderStream() async {
    try {
      currentOrdersStream?.cancel();
      currentOrdersStream = FBFireStore.orders
          // .where('userData.docId', isEqualTo: currentUserData!.docId)
          .where('userData.docId', isEqualTo: FBAuth.auth.currentUser!.uid)
          .where('isCompleted', isEqualTo: false)
          // .where('isPaid', isEqualTo: true)
          .orderBy('time', descending: true)
          .snapshots()
          .listen((event) {
            currentOrders =
                event.docs.map((e) => OrderModel.fromSnap(e)).toList();
            update();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  addOnStream() async {
    try {
      addOnsStream?.cancel();
      addOnsStream = FBFireStore.outlets
          .doc(currentUserData?.selectedOutletDocId)
          .collection("addOn")
          .where("blocked", isEqualTo: false)
          .snapshots()
          .listen((event) {
            addOns = event.docs.map((e) => AddOnModel.fromSnap(e)).toList();
            update();
          });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  setNewUserData() async {
    try {
      await FBFireStore.users
          .doc(FBAuth.auth.currentUser?.uid)
          .set(UserModel.newUserJson());
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future addToCart(
    String foodDocId,
    String variantId,
    List selectedAddons, {
    required UserCartItem? userCartItem,
  }) async {
    boolvariantId = variantId;
    plusMinusAllowed = false;
    valCoupon = true;
    update();
    // if(added) qty ++;
    final crtItm = cartItems.firstWhereOrNull(
      (element) =>
          element.foodDocId == foodDocId &&
          element.variantId == variantId &&
          listsAreSame(element.addonDocIdList, selectedAddons),
    );
    // final available = foods
    //         .firstWhereOrNull((element) => element.docId == foodDocId)
    //         ?.availableNow ??
    //     true;
    // print(available);

    if (crtItm != null) {
      crtItm.qty++;
      await addToDBCart(crtItm, true);
    }
    // else {add to cartitems}
    else {
      final newUserCartItem =
          userCartItem == null
              ? UserCartItem(
                foodDocId: foodDocId,
                qty: 1,
                variantId: variantId,
                addonDocIdList: [...selectedAddons],
                itemId: getRandomId(6),
                // addedAt: DateTime.now(),
              )
              : UserCartItem(
                foodDocId: userCartItem.foodDocId,
                qty: userCartItem.qty,
                variantId: userCartItem.variantId,
                addonDocIdList: [...userCartItem.addonDocIdList],
                itemId: userCartItem.itemId,
                // addedAt: userCartItem.addedAt,
              );
      cartItems.add(newUserCartItem);
      await addToDBCart(newUserCartItem, false);
    }
    // await Future.delayed(Duration(milliseconds: 200));

    calcCart();
  }

  addToDBCart(UserCartItem item, bool addQty) async {
    await FBFireStore.users.doc(currentUserData?.docId).update({
      if (addQty)
        'userCartItems.${item.itemId}.qty': FieldValue.increment(1)
      else
        'userCartItems.${item.itemId}': item.toJson(),
    });
    update();
  }

  removeFromDBCart(UserCartItem item, bool subQty) async {
    await FBFireStore.users.doc(currentUserData?.docId).update({
      if (subQty)
        'userCartItems.${item.itemId}.qty': FieldValue.increment(-1)
      else
        'userCartItems.${item.itemId}': FieldValue.delete(),
    });
    update();
  }

  removeFromCart(
    String foodDocId,
    String variantId, [
    bool removeAll = false,
  ]) async {
    boolvariantId = variantId;
    plusMinusAllowed = false;
    valCoupon = true;
    update();
    final crtItm = cartItems.firstWhereOrNull(
      (element) =>
          element.foodDocId == foodDocId && element.variantId == variantId,
    );
    if (removeAll) {
      // remove item
      cartItems.remove(crtItm);
    } else {
      print('crtItm?.qty ${crtItm?.qty}');
      if (crtItm!.qty > 1) {
        crtItm.qty--;
        await removeFromDBCart(crtItm, true);
      } else {
        cartItems.remove(crtItm);
        await removeFromDBCart(crtItm, false);
      }
    }
    // await Future.delayed(Duration(milliseconds: 200));
    await calcCart();
  }

  calcCart() async {
    if (selectedCoupon != null) {
      if (subTotal < selectedCoupon!.minOrderValue && valCoupon) {
        showAppSnackBar(
          "Min order value for coupon is ${selectedCoupon?.minOrderValue}",
        );
        selectedCoupon = null;
        await calcCart();
        return;
      } else {
        await calcDiscount();
        await calcPrices(0);
      }
    } else {
      await calcDiscount();
      await calcPrices(0);
    }

    boolvariantId = null;
    // await Future.delayed(Duration(milliseconds: 1000));
    plusMinusAllowed = true;
    valCoupon = false;
    update();
    return;
    await calcPrices(0);

    await calcDiscount();
    debugPrint('Discount after calculation $discountAmount');
    // Calculate Split Amount For Discount
    int totalItemsSold = cartItems.length;
    num discountSplitAmount = discountAmount / totalItemsSold;
    // debugPrint('Split Amount $discountSplitAmount');
    // Calculate prices
    // await calcRewardPoints();
    await calcPrices(0);
    print("before coupon==== ${subTotal}");
    await validateCoupon();

    debugPrint(
      'SUB: $subTotal, SUB-D: $subTotalBeforeDis, SPLIT: $discountSplitAmount, TAX: $totalTax, DIS $discountAmount, TOTAL: $grandTotal',
    );

    boolvariantId = null;
    // await Future.delayed(Duration(milliseconds: 1000));
    plusMinusAllowed = true;
    update();
  }
  /*   calcCart() async {
    await calcPrices(0);

    await calcDiscount();
    debugPrint('Discount after calculation $discountAmount');
    // Calculate Split Amount For Discount
    int totalItemsSold = cartItems.length;
    num discountSplitAmount = discountAmount / totalItemsSold;
    // debugPrint('Split Amount $discountSplitAmount');
    // Calculate prices
    // await calcRewardPoints();
    await calcPrices(0);
    print("before coupon==== ${subTotal}");
    await validateCoupon();

    debugPrint(
      'SUB: $subTotal, SUB-D: $subTotalBeforeDis, SPLIT: $discountSplitAmount, TAX: $totalTax, DIS $discountAmount, TOTAL: $grandTotal',
    );

    boolvariantId = null;
    // await Future.delayed(Duration(milliseconds: 1000));
    plusMinusAllowed = true;
    update();
  } */

  calcDiscount() async {
    try {
      discountAmount = 0;
      rewardPointsUsed = 0;
      // ===== Calculate Coupon Discount ===== //
      if (selectedCoupon != null) {
        num couponVal = 0;
        if (selectedCoupon!.isPercentage) {
          // Percentage type Coupon
          couponVal = subTotal * (selectedCoupon!.value / 100);
          // debugPrint('>>>> $couponVal - ${selectedCoupon!.value}');
          if (couponVal > selectedCoupon!.maxDiscount) {
            couponVal = selectedCoupon!.maxDiscount;
          }
        } else {
          // Flat off type Coupon
          couponVal = selectedCoupon!.value;
        }
        debugPrint('$couponVal');
        discountAmount = couponVal;
      }
      // ===== Calculate Reward Points discount ===== //
      // return;
      final rewardPoints = currentUserData?.rewardPoints ?? 0;
      if ((subTotal - rewardPoints) >= 0) {
        rewardPointsUsed = rewardPoints;
        discountAmount += rewardPointsUsed;
      } else {
        rewardPointsUsed = rewardPoints - subTotal;
        discountAmount += rewardPointsUsed;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  calcPrices(num splitAmount) async {
    try {
      subTotal = 0;
      subTotalBeforeDis = 0;
      totalTax = 0;
      grandTotal = 0;
      num totalBeforeDiscount = cartItems.fold(0, (previousValue, crtItm) {
        final foodItem = foods.firstWhereOrNull(
          (ele) => ele.docId == crtItm.foodDocId,
        );
        final variant = foodItem!.variants.firstWhereOrNull(
          (e) => e.id == crtItm.variantId,
        );

        return previousValue + (variant!.price * crtItm.qty);
      });
      debugPrint('Total before disc $totalBeforeDiscount');
      for (var crtItm in cartItems) {
        final foodItem = foods.firstWhereOrNull(
          (element) => element.docId == crtItm.foodDocId,
        );

        final variant = foodItem!.variants.firstWhereOrNull(
          (e) => e.id == crtItm.variantId,
        );

        // Food calcs
        final ratio = (variant!.price * crtItm.qty) / totalBeforeDiscount;
        num priceToCalcNow =
            (variant.price * crtItm.qty) - (ratio * discountAmount);
        debugPrint('Price to calc: $priceToCalcNow');
        subTotal += priceToCalcNow;
        subTotalBeforeDis += variant.price * crtItm.qty;
        totalTax += ((priceToCalcNow * foodItem.taxPercentage) / 100);
        grandTotal +=
            (((priceToCalcNow) +
                ((priceToCalcNow * foodItem.taxPercentage) / 100)));

        // Addon Calcs
        final addOnList =
            addOns
                .where(
                  (element) => crtItm.addonDocIdList.contains(element.docId),
                )
                .toList();
        for (var adn in addOnList) {
          num adnCalPrice = adn.price * crtItm.qty;
          subTotal += (adnCalPrice);
          subTotalBeforeDis += adnCalPrice;
          totalTax += (adnCalPrice * adn.tax) / 100;
          grandTotal += ((adnCalPrice) + ((adnCalPrice * adn.tax) / 100));
        }
      }
      rewardPointsEarned =
          (subTotal * (currentOutlet?.rewardPoints ?? 0)) / 100;
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  /* OLD WORKING CODE */
  /*  calcPrices(num splitAmount) async {
    try {
      subTotal = 0;
      subTotalBeforeDis = 0;
      totalTax = 0;
      grandTotal = 0;
      for (var crtItm in cartItems) {
        final foodItem = foods.firstWhereOrNull(
          (element) => element.docId == crtItm.foodDocId,
        );

        final variant = foodItem!.variants.firstWhereOrNull(
          (e) => e.id == crtItm.variantId,
        );
        final addOnList =
            addOns
                .where(
                  (element) => crtItm.addonDocIdList.contains(element.docId),
                )
                .toList();
        // Food calcs
        num priceToCalcNow = variant!.price - (splitAmount / crtItm.qty);
        subTotal += priceToCalcNow * crtItm.qty;
        subTotalBeforeDis += variant.price * crtItm.qty;
        totalTax +=
            ((priceToCalcNow * foodItem.taxPercentage) / 100) * crtItm.qty;
        grandTotal +=
            (((priceToCalcNow) +
                ((priceToCalcNow * foodItem.taxPercentage) / 100))) *
            crtItm.qty;
        // Addon Calcs
        for (var adn in addOnList) {
          num adnCalPrice = adn.price;
          subTotal += adnCalPrice;
          subTotalBeforeDis += adnCalPrice;
          totalTax += (adnCalPrice * adn.tax) / 100;
          grandTotal += ((adnCalPrice) + ((adnCalPrice * adn.tax) / 100)) - 0;
        }
      }
      rewardPointsEarned =
          (subTotal * (currentOutlet?.rewardPoints ?? 0)) / 100;
    } catch (e) {
      debugPrint(e.toString());
    }
  }
 */
  applyCoupon(DiscountCouponModel coupon) async {
    try {
      valCoupon = true;
      selectedCoupon = coupon;
      // validateCoupon();
      calcCart();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  removeCoupon(DiscountCouponModel coupon) async {
    try {
      valCoupon = true;
      selectedCoupon = null;
      // validateCoupon();
      calcCart();
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  validateCoupon() async {
    try {
      // print("IN validate");
      if (selectedCoupon == null) return;
      // print('$subTotal - $subTotalBeforeDis ${selectedCoupon!.minOrderValue}');
      print("SubTotal ======= ${subTotal}");
      if (subTotal < selectedCoupon!.minOrderValue) {
        showAppSnackBar(
          "Min order value for coupon is ${selectedCoupon?.minOrderValue}",
        );
        selectedCoupon = null;
        calcCart();
        return;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  setFcmToken(List tokens) async {
    try {
      if (fcmTokenChecked) return;
      fcmTokenChecked = true;
      String? fcmToken = await FirebaseMessaging.instance.getToken();
      debugPrint('FCMTOKEN::::::: $fcmToken');
      if (fcmToken != null) {
        if (tokens.length > 7) {
          await FBFireStore.users.doc(FBAuth.auth.currentUser?.uid).update({
            'tokens': [fcmToken],
          });
        } else {
          await FBFireStore.users.doc(FBAuth.auth.currentUser?.uid).update({
            'tokens': FieldValue.arrayUnion([fcmToken]),
          });
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future clearCart() async {
    cartItems.clear();
    selectedCoupon = null;
    await FBFireStore.users.doc(currentUserData?.docId).update({
      'userCartItems': {},
    });
    update();
  }

  checkFoodTimeFrames() {
    debugPrint("Checking food availability....");
    final dateTimeNow = DateTime.now();
    for (var food in foods) {
      bool available = false;
      for (var timeFrame in food.timeFrames) {
        final startingAt = DateTime(
          dateTimeNow.year,
          dateTimeNow.month,
          dateTimeNow.day,
          timeFrame.startHour,
          timeFrame.startMin,
        );
        final endingAt = DateTime(
          dateTimeNow.year,
          dateTimeNow.month,
          dateTimeNow.day,
          timeFrame.endHour,
          timeFrame.endMin,
        );
        if (dateTimeNow.isAfter(startingAt) && dateTimeNow.isBefore(endingAt)) {
          available = true;
        }
      }
      food.availableNow = available || food.timeFrames.isEmpty;
      if (!food.availableNow) removeIfInCart(food);
    }
    calcCart();
    // update();
  }

  removeIfInCart(FoodModel food) async {
    // cartItems.removeWhere((element) => element.foodDocId == food.docId);
    cartItems.removeWhere((element) {
      bool inCart = element.foodDocId == food.docId;
      if (inCart) removeFromDBCart(element, false);
      return inCart;
    });
    update();
  }

  foodTimeChecker() async {
    try {
      Timer.periodic(const Duration(seconds: foodTimeFrameCheckInterval), (
        timer,
      ) {
        checkFoodTimeFrames();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
