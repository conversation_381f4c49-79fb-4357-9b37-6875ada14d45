import 'package:cloud_firestore/cloud_firestore.dart';

class RiderModel {
  String docId, name, outletId;
  String? licence;
  num? number, lat, lng;
  bool blocked, available;

  RiderModel({
    required this.docId,
    required this.name,
    required this.outletId,
    required this.number,
    required this.available,
    this.licence,
    required this.blocked,
    required this.lat,
    required this.lng,
  });

  factory RiderModel.fromSnap(
    QueryDocumentSnapshot<Map<String, dynamic>> json,
  ) {
    return RiderModel(
      docId: json.id,
      name: json["name"],
      outletId: json["outletId"],
      number: json["number"],
      available: json["available"],
      blocked: json["blocked"],
      licence: json["licence"],
      lat: json.data()['lat'],
      lng: json.data()['lng'],
    );
  }
  factory RiderModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> json) {
    return RiderModel(
      docId: json.id,
      name: json["name"],
      outletId: json["outletId"],
      number: json["number"],
      available: json["available"],
      blocked: json["blocked"],
      licence: json["licence"],
      lat: json.data()?['lat'],
      lng: json.data()?['lng'],
    );
  }
}

//   factory FoodItemModel.fromJson(Map<String, dynamic> json) {
//     print(json);
//     return CategoryModel(
//       image: json["image"],
//       name: json["name"],
//     );
//   }
