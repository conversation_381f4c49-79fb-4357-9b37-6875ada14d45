import 'package:cloud_firestore/cloud_firestore.dart';

class SuperAdminModel {
  String docId, title, desc;
  Timestamp time;

  SuperAdminModel({
    required this.docId,
    required this.title,
    required this.desc,
    required this.time,
  });

//   factory FoodItemModel.fromJson(Map<String, dynamic> json) {
//     print(json);
//     return CategoryModel(
//       image: json["image"],
//       name: json["name"],
//     );
//   }
}
