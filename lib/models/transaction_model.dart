import 'package:cloud_firestore/cloud_firestore.dart';

class TransactionModel {
  final String docId, uId, transId, method, orderDocId;
  num amount, lat, long, taxOnOrder;
  Timestamp time;

  TransactionModel({
    required this.docId,
    required this.uId,
    required this.transId,
    required this.method,
    required this.orderDocId,
    required this.amount,
    required this.lat,
    required this.long,
    required this.taxOnOrder,
    required this.time,
  });

//   factory FoodItemModel.fromJson(Map<String, dynamic> json) {
//     print(json);
//     return CategoryModel(
//       image: json["image"],
//       name: json["name"],
//     );
//   }
}
