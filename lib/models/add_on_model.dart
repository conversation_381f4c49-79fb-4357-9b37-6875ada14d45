import 'package:cloud_firestore/cloud_firestore.dart';

class AddOnModel {
  String docId;
  String name;
  num price;
  num tax;
  bool blocked;

  AddOnModel({
    required this.docId,
    required this.name,
    required this.price,
    required this.tax,
    required this.blocked,
  });
  factory AddOnModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return AddOnModel(
      docId: json.id,
      name: json["name"],
      price: json["price"],
      tax: json["tax"],
      blocked: json["blocked"],
    );
  }
}
//   factory FoodItemModel.fromJson(Map<String, dynamic> json) {
//     print(json);
//     return CategoryModel(
//       image: json["image"],
//       name: json["name"],
//     );
//   }
