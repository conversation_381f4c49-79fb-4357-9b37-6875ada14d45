import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

class CategoryModel {
  final String docId;
  final String name;
  final String image;
  final bool blocked;
  final GlobalKey catKey;
  final int priorityNo;

  CategoryModel({
    required this.docId,
    required this.priorityNo,
    required this.name,
    required this.image,
    required this.blocked,
    required this.catKey,
  });

  factory CategoryModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return CategoryModel(
      docId: json.id,
      image: json["image"],
      blocked: json["blocked"],
      name: json["name"],
      catKey: GlobalKey(debugLabel: json["name"]),
      priorityNo: json.data()["priorityNo"] ?? 99,
    );
  }
}
//   factory FoodItemModel.fromJson(Map<String, dynamic> json) {
//     print(json);
//     return CategoryModel(
//       image: json["image"],
//       name: json["name"],
//     );
//   }
