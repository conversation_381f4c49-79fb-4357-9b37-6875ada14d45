import 'package:cloud_firestore/cloud_firestore.dart';

import 'time_frame_model.dart';

class OutletModel {
  String docId, note, outletName, outletAddresss;
  bool cashOnDelivery, takeAway, delivery, blocked;
  List<TimeFrameModel> timeFrames;
  num minOrder, rewardPoints, radius, lat, long;
  String? adminName, adminDocId;
  Timestamp createdAt;

  OutletModel({
    required this.docId,
    required this.note,
    required this.outletName,
    required this.outletAddresss,
    required this.cashOnDelivery,
    required this.takeAway,
    required this.delivery,
    required this.blocked,
    required this.timeFrames,
    required this.minOrder,
    required this.rewardPoints,
    required this.radius,
    required this.lat,
    required this.long,
    required this.createdAt,
    this.adminDocId,
    this.adminName,
  });

  factory OutletModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return OutletModel(
      docId: json.id,
      note: json['note'],
      outletName: json["outletName"],
      outletAddresss: json["outletAddresss"],
      cashOnDelivery: json['cashOnDelivery'],
      takeAway: json['takeAway'],
      delivery: json['delivery'],
      blocked: json['blocked'],
      timeFrames: Map.castFrom(json['timeFrames'])
          .entries
          .map((e) => TimeFrameModel.fromJson(e.key, e.value))
          .toList(),
      // timeFrame: [json['timeFrame']],
      minOrder: json['minOrder'],
      rewardPoints: json['rewardPoints'],
      radius: json['radius'],
      lat: json['lat'],
      long: json['long'],
      createdAt:
          json.data().containsKey('createdAt') ? json["createdAt"] : null,
      adminDocId: json['adminDocId'],
      adminName: json['adminName'],
    );
  }
  factory OutletModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> json) {
    return OutletModel(
      docId: json.id,
      note: json['note'],
      outletName: json["outletName"],
      outletAddresss: json["outletAddresss"],
      cashOnDelivery: json['cashOnDelivery'],
      takeAway: json['takeAway'],
      delivery: json['delivery'],
      blocked: json['blocked'],
      timeFrames: Map.castFrom(json['timeFrames'])
          .entries
          .map((e) => TimeFrameModel.fromJson(e.key, e.value))
          .toList(),
      minOrder: json['minOrder'],
      rewardPoints: json['rewardPoints'],
      radius: json['radius'],
      lat: json['lat'],
      long: json['long'],
      createdAt:
          json.data()!.containsKey('createdAt') ? json["createdAt"] : null,
      adminDocId: json['adminDocId'],
      adminName: json['adminName'],
    );
  }
}
