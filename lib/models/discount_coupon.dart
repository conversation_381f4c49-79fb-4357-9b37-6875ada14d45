import 'package:cloud_firestore/cloud_firestore.dart';

class DiscountCouponModel {
  late String docId, name, couponCode, outletId;
  late num value, minOrderValue, maxDiscount;
  late bool isPercentage, blocked;

  DiscountCouponModel({
    required this.docId,
    required this.name,
    required this.couponCode,
    required this.outletId,
    required this.blocked,
    required this.value,
    required this.minOrderValue,
    required this.maxDiscount,
    required this.isPercentage,
  });

  factory DiscountCouponModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return DiscountCouponModel(
      docId: json.id,
      name: json["name"],
      couponCode: json["couponCode"],
      outletId: json["outletId"],
      blocked: json["blocked"],
      value: json["value"],
      minOrderValue: json["minOrderValue"],
      maxDiscount: json["maxDiscount"],
      isPercentage: json["isPercentage"],
    );
  }

  DiscountCouponModel.fromJson(Map<String, dynamic> json) {
    blocked = json["blocked"];
    couponCode = json["couponCode"];
    isPercentage = json["isPercentage"];
    maxDiscount = json["maxDiscount"];
    minOrderValue = json["minOrderValue"];
    name = json['name'];
    outletId = json["outletId"];
    docId = json['docId'];
    value = json["value"];
  }

  Map<String, dynamic> toJson() {
    return {
      'docId': docId,
      'name': name,
      'couponCode': couponCode,
      'outletId': outletId,
      'blocked': blocked,
      'value': value,
      'minOrderValue': minOrderValue,
      'maxDiscount': maxDiscount,
      'isPercentage': isPercentage,
    };
  }
}
//   factory FoodItemModel.fromJson(Map<String, dynamic> json) {
//     print(json);
//     return CategoryModel(
//       image: json["image"],
//       name: json["name"],
//     );
//   }
