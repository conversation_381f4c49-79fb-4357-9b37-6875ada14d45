import 'package:cloud_firestore/cloud_firestore.dart';

class AdminModel {
  String docId;
  String? outletId;
  bool blocked;
  bool isAdmin;
  String name;
  String? outletName;
  Timestamp? createdAt;
  String email;
  String number;

  AdminModel({
    required this.docId,
    this.outletId,
    required this.blocked,
    required this.isAdmin,
    required this.name,
    required this.outletName,
    required this.createdAt,
    required this.email,
    required this.number,
  });

  factory AdminModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return AdminModel(
      docId: json.id,
      outletId: json["outletId"],
      blocked: json["blocked"],
      isAdmin: json["isAdmin"],
      name: json["name"],
      outletName: json["outletName"],
      createdAt:
          json.data().containsKey('createdAt') ? json["createdAt"] : null,
      email: json["email"],
      number: json["number"],
    );
  }
  factory AdminModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> json) {
    return AdminModel(
      docId: json.id,
      outletId: json["outletId"],
      blocked: json["blocked"],
      isAdmin: json["isAdmin"],
      name: json["name"],
      outletName: json["outletName"],
      createdAt:
          json.data()!.containsKey('createdAt') ? json["createdAt"] : null,
      email: json["email"],
      number: json["number"],
    );
  }
}

//   factory FoodItemModel.fromJson(Map<String, dynamic> json) {
//     print(json);
//     return CategoryModel(
//       image: json["image"],
//       name: json["name"],
//     );
//   }