import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:swagat_user/shared/firebase.dart';
import 'address_model.dart';

class UserModel {
  String docId;
  String name;
  String number;
  num rewardPoints;
  List<String> favourite;
  List<AddressModel> addresses;
  List<UserCartItem> userCartItems;
  String? selectedOutletDocId;
  String? defaultAddressId;

  UserModel({
    required this.docId,
    required this.name,
    required this.number,
    required this.rewardPoints,
    required this.favourite,
    required this.addresses,
    required this.selectedOutletDocId,
    required this.userCartItems,
    required this.defaultAddressId,
  });

  factory UserModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> json) {
    return UserModel(
      docId: json.id,
      name: json['name'],
      number: json['number'],
      rewardPoints: json['rewardPoints'],
      favourite: List.castFrom<dynamic, String>(
        json["favourite"],
      ), //json['favourite'],
      addresses:
          Map.castFrom(json['addresses']).entries
              .map((e) => AddressModel.fromJson(e.key, e.value))
              .toList(), //json['address'],
      userCartItems:
          Map.castFrom(json['userCartItems']).entries
              .map((e) => UserCartItem.fromJson(e.key, e.value))
              .toList(), //json['userCartItem'],
      selectedOutletDocId: json['selectedOutletDocId'],
      defaultAddressId: json.data()?['defaultAddressId'],
    );
  }

  static newUserJson() {
    return {
      'name': "",
      'number': FBAuth.auth.currentUser?.phoneNumber,
      'rewardPoints': 0,
      'favourite': [],
      'addresses': {},
      'selectedOutletDocId': null,
      'userCartItems': {},
      'defaultAddressId': "",
    };
  }
}

class UserCartItem {
  final String itemId;
  final String foodDocId;
  final String variantId;
  final List<String> addonDocIdList;
  // final DateTime addedAt;
  int qty;

  UserCartItem({
    required this.itemId,
    required this.foodDocId,
    required this.qty,
    required this.variantId,
    // required this.addedAt,
    required this.addonDocIdList,
  });

  Map<String, dynamic> toJson() {
    return {
      'itemId': itemId,
      'foodDocId': foodDocId,
      'variantId': variantId,
      // 'addedAt': DateTime.now(),
      'addonDocIdList': addonDocIdList,
      'qty': qty,
    };
  }

  factory UserCartItem.fromJson(String ky, Map<String, dynamic> json) {
    return UserCartItem(
      itemId: ky,
      foodDocId: json['foodDocId'],
      variantId: json['variantId'],
      // addedAt:
      //     json.containsKey('addedAt')
      //         ? (json['addedAt'] as Timestamp).toDate()
      //         : DateTime.now(),
      addonDocIdList: List<String>.from(json['addonDocIdList']),
      qty: json['qty'],
    );
  }
}
