class AddressModel {
  late String id,
      name,
      flat,
      area,
      landmark,
      city,
      state,
      addressType,
      contact,
      location;
  late num lat, long;

  AddressModel({
    required this.id,
    required this.name,
    required this.flat,
    required this.area,
    required this.landmark,
    required this.city,
    required this.state,
    required this.addressType,
    required this.lat,
    required this.long,
    required this.contact,
    required this.location,
  });

  toJson() {
    return {
      'id': id,
      'name': name,
      'flat': flat,
      'area': area,
      'landmark': landmark,
      'city': city,
      'state': state,
      'addressType': addressType,
      'lat': lat,
      'long': long,
      'contact': contact,
      'location': location,
    };
  }

  AddressModel.fromJson(String? tId, Map<String, dynamic> json) {
    id = tId ?? json['id'];
    name = json["name"];
    flat = json["flat"];
    area = json["area"];
    landmark = json["landmark"];
    city = json["city"];
    state = json["state"];
    addressType = json["addressType"];
    lat = json["lat"];
    long = json["long"];
    contact = json["contact"] ?? "";
    location = json["location"] ?? "";
  }
}
