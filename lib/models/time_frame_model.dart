class TimeFrameModel {
  late String id;
  late int startHour, endHour, startMin, endMin;

  TimeFrameModel({
    required this.id,
    required this.startHour,
    required this.endHour,
    required this.startMin,
    required this.endMin,
  });

  TimeFrameModel.fromJson(String tId, Map<String, dynamic> json) {
    id = tId;
    startHour = json["startHour"];
    endHour = json["endHour"];
    startMin = json["startMin"];
    endMin = json["endMin"];
  }

  toJson() {
    return {
      id: {
        'startHour': startHour,
        'endHour': endHour,
        'startMin': startMin,
        'endMin': endMin,
      }
    };
  }

//   factory FoodItemModel.fromJson(Map<String, dynamic> json) {
//     print(json);
//     return CategoryModel(
//       image: json["image"],
//       name: json["name"],
//     );
//   }
}
