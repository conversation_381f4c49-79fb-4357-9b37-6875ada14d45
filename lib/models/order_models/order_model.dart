import 'package:cloud_firestore/cloud_firestore.dart';
import '../address_model.dart';
import '../discount_coupon.dart';
import 'food_in_order_model.dart';
import 'rider_in_order_model.dart';
import 'user_data_in_order.dart';

class OrderModel {
  String docId;
  String orderId;
  String? uId;
  Timestamp? time;
  String outletDocId;
  String type;
  num totalAmountPaid;
  num totalTax;
  bool isPaid;
  String status;
  bool isCompleted;
  UserDataInOrderModel? userData;
  AddressModel? userAddress;
  RiderInOrderModel? rider;
  String? transId;
  num discountAmount;
  DiscountCouponModel? discountCoupon;
  num rewardPointsUsed;
  num rewardPointsEarned;
  num otp;
  Map<String, dynamic>? paymentMethod;
  bool? cashOnDelivery;
  String orderNote;
  Timestamp? deliveredOn;
  Timestamp? takeAwayOn;
  Timestamp? paidOn;
  List<FoodInOrderModel> foodItems;

  OrderModel({
    required this.docId,
    required this.orderId,
    this.uId,
    required this.time,
    required this.outletDocId,
    required this.type,
    required this.totalAmountPaid,
    required this.totalTax,
    required this.isPaid,
    required this.status,
    required this.userData,
    required this.userAddress,
    required this.rider,
    required this.isCompleted,
    this.transId,
    this.paymentMethod,
    required this.discountAmount,
    this.discountCoupon,
    required this.rewardPointsUsed,
    required this.rewardPointsEarned,
    required this.otp,
    this.cashOnDelivery,
    this.paidOn,
    required this.orderNote,
    required this.deliveredOn,
    required this.takeAwayOn,
    required this.foodItems,
  });

  factory OrderModel.fromSnap(QueryDocumentSnapshot jsonD) {
    final data = jsonD.data() as Map<String, dynamic>;
    return OrderModel(
      cashOnDelivery: jsonD['cashOnDelivery'],
      transId: jsonD['transId'],
      docId: jsonD.id,
      uId: jsonD['uId'],
      isCompleted: jsonD['isCompleted'],
      orderId: jsonD["orderId"],
      time: jsonD["time"],
      outletDocId: jsonD["outletDocId"],
      type: jsonD["type"],
      totalAmountPaid: jsonD["totalAmountPaid"],
      totalTax: jsonD["totalTax"],
      isPaid: jsonD["isPaid"],
      status: jsonD["status"],
      userData: UserDataInOrderModel.fromJson(jsonD["userData"]),
      // userAddress: AddressModel.fromJson(jsonD["userAddress"]),
      userAddress: AddressModel.fromJson(null, jsonD["userAddress"]),
      rider:
          jsonD['rider'] == null
              ? null
              : RiderInOrderModel.fromJson(jsonD["rider"]),
      discountAmount: jsonD["discountAmount"],
      rewardPointsUsed: jsonD["rewardPointsUsed"],
      rewardPointsEarned: jsonD["rewardPointsEarned"],
      otp: jsonD["otp"],
      orderNote: jsonD["orderNote"],
      deliveredOn: jsonD["deliveredOn"],
      paidOn: data.containsKey('paidOn') ? jsonD["paidOn"] : null,
      paymentMethod:
          data.containsKey('paymentMethod') ? jsonD["paymentMethod"] : null,
      takeAwayOn: jsonD["takeAwayOn"],
      foodItems:
          Map.castFrom(jsonD['foodItems']).entries
              .map((e) => FoodInOrderModel.fromJson(e.key, e.value))
              .toList(),
      discountCoupon:
          jsonD['disCoupon'] == null
              ? null
              : DiscountCouponModel.fromJson(jsonD['disCoupon']),
    );
  }
  factory OrderModel.fromDoc(DocumentSnapshot jsonD) {
    final data = jsonD.data() as Map<String, dynamic>;

    return OrderModel(
      cashOnDelivery: jsonD['cashOnDelivery'],
      transId: jsonD['transId'],
      docId: jsonD.id,
      uId: jsonD['uId'],
      isCompleted: jsonD['isCompleted'],
      orderId: jsonD["orderId"],
      time: jsonD["time"],
      outletDocId: jsonD["outletDocId"],
      type: jsonD["type"],
      totalAmountPaid: jsonD["totalAmountPaid"],
      totalTax: jsonD["totalTax"],
      isPaid: jsonD["isPaid"],
      status: jsonD["status"],
      userData: UserDataInOrderModel.fromJson(jsonD["userData"]),
      // userAddress: AddressModel.fromJson(jsonD["userAddress"]),
      userAddress: AddressModel.fromJson(null, jsonD["userAddress"]),
      rider:
          jsonD['rider'] == null
              ? null
              : RiderInOrderModel.fromJson(jsonD["rider"]),
      discountAmount: jsonD["discountAmount"],
      rewardPointsUsed: jsonD["rewardPointsUsed"],
      rewardPointsEarned: jsonD["rewardPointsEarned"],
      paymentMethod:
          data.containsKey('paymentMethod') ? jsonD["paymentMethod"] : null,
      otp: jsonD["otp"],
      paidOn: (data.containsKey('paidOn')) ? jsonD["paidOn"] : null,
      orderNote: jsonD["orderNote"],
      deliveredOn: jsonD["deliveredOn"],
      takeAwayOn: jsonD["takeAwayOn"],
      foodItems:
          Map.castFrom(jsonD['foodItems']).entries
              .map((e) => FoodInOrderModel.fromJson(e.key, e.value))
              .toList(),
      discountCoupon:
          jsonD['disCoupon'] == null
              ? null
              : DiscountCouponModel.fromJson(jsonD['disCoupon']),
    );
  }
}
