import 'addon_in_order_model.dart';

class FoodInOrderModel {
  late String id, foodDocId, name, variantName, variantId;
  late bool status;
  late num tax, price, qty;
  late List<AddOnOrderModel> addOns;

  FoodInOrderModel({
    required this.id,
    required this.foodDocId,
    required this.name,
    required this.variantName,
    required this.variantId,
    required this.status,
    required this.tax,
    required this.price,
    required this.qty,
    required this.addOns,
  });
  FoodInOrderModel.fromJson(String fId, Map<String, dynamic> json) {
    id = fId;
    foodDocId = json["foodDocId"];
    name = json["name"];
    price = json["price"];
    qty = json["qty"];
    status = json["status"];
    tax = json["tax"];
    variantId = json["variantId"];
    variantName = json["variantName"];
    addOns = Map.castFrom(json['addons'])
        .entries
        .map((e) => AddOnOrderModel.fromJson(e.key, e.value))
        .toList();
  }
}
