class UserDataInOrderModel {
  String? docId, name, address, number;
  // num? lat, long;

  UserDataInOrderModel({
    required this.docId,
    required this.name,
    required this.number,
    // required this.lat,
    // required this.long,
  });

  UserDataInOrderModel.fromJson(Map<String, dynamic> json) {
    docId = json["docId"];
    name = json["name"];
    number = json["number"];
    // lat = json['lat'];
    // long = json['long'];
  }
}
