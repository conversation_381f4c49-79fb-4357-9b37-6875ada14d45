import 'package:cloud_firestore/cloud_firestore.dart';

import 'time_frame_model.dart';
import 'variants_model.dart';

class FoodModel {
  String docId;
  String name;
  String imageUrl;
  String description;
  List<String> categories;
  List<String> addOns;
  bool blocked;
  bool special;
  bool availableNow;
  List<VariantModel> variants;
  List<TimeFrameModel> timeFrames;
  num taxPercentage;

  FoodModel({
    required this.docId,
    required this.name,
    required this.imageUrl,
    required this.description,
    required this.categories,
    required this.addOns,
    required this.blocked,
    required this.special,
    required this.variants,
    required this.timeFrames,
    required this.taxPercentage,
    this.availableNow = true,
  });

  factory FoodModel.fromSnap(QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return FoodModel(
      docId: json.id,
      name: json["name"],
      imageUrl: json["image"],
      description: json["description"],
      categories: List.castFrom<dynamic, String>(json["categories"]),
      addOns: List.castFrom<dynamic, String>(json["addOns"]),
      blocked: json["blocked"],
      special: json["special"] ?? false,
      taxPercentage: json["taxPercentage"],
      variants: Map.castFrom(json['variants'])
          .entries
          .map((e) => VariantModel.fromJson(e.key, e.value))
          .toList(),
      timeFrames: Map.castFrom(json['timeFrames'])
          .entries
          .map((e) => TimeFrameModel.fromJson(e.key, e.value))
          .toList(),
    );
  }

  factory FoodModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> json) {
    return FoodModel(
      docId: json.id,
      name: json["name"],
      imageUrl: json["image"],
      description: json["description"],
      categories: List.castFrom<dynamic, String>(json["categories"]),
      addOns: List.castFrom<dynamic, String>(json["addOns"]),
      blocked: json["blocked"],
      taxPercentage: json["taxPercentage"],
      variants: Map.castFrom(json['variants'])
          .entries
          .map((e) => VariantModel.fromJson(e.key, e.value))
          .toList(),
      timeFrames: Map.castFrom(json['timeFrames'])
          .entries
          .map((e) => TimeFrameModel.fromJson(e.key, e.value))
          .toList(),
      special: json["special"],
    );
  }
}

//   factory FoodItemModel.fromJson(Map<String, dynamic> json) {
//     print(json);
//     return CategoryModel(
//       image: json["image"],
//       name: json["name"],
//     );
//   }
