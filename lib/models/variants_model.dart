class VariantModel {
  late final String id;
  late final String nameOrSize;
  late final num price;

  VariantModel({
    required this.id,
    required this.nameOrSize,
    required this.price,
  });

  VariantModel.fromJson(String vId, Map<String, dynamic> json) {
    id = vId;
    nameOrSize = json["nameOrSize"];
    price = json["price"];
  }
}

  // factory VariantModel.fromJson(Map<String, dynamic> json) {
  //   print(json);
  //   return VariantModel(
  //     image: json["image"],
  //     name: json["name"],
  //   );
  // }